<template>
  <div class="login-container" :style="{ backgroundImage: `url('/images/bg1.jpg')`,backgroundSize: 'cover' }">
            <!-- 左侧品牌展示区 -->
            <div class="login-brand-container" >
                <div  class="login-brand-bg"></div>
                <div class="login-brand-overlay"></div>
                <div class="login-brand-content">
                    <div>
                        <h1 class="login-brand-title">志友中台</h1>
                        <p class="login-brand-subtitle">现代化企业管理平台</p>
                    </div>
                    <div class="login-brand-text">
                        <h2 class="login-brand-slogan">
                            智能管理<br />
                            高效协作<br />
                            数字未来
                        </h2>
                        <p class="login-brand-description">
                            为企业提供全方位的数字化管理解决方案，助力企业数字化转型升级
                        </p>
                    </div>
                </div>
            </div>
            <!-- 右侧登录表单区 -->
            <div class="login-form-container">
                <div class="login-form-wrapper">
                    <!-- 移动端 Logo -->
                    <div class="login-form-mobile-header">
                        <h1 class="login-form-mobile-title">志友中台</h1>
                        <!--<p class="login-form-mobile-subtitle">欢迎回来</p> -->
                    </div>
                    <!-- 桌面端欢迎语 -->
                    <div class="login-form-desktop-header">
                        <h2 class="login-form-desktop-title">欢迎回来</h2>
                        <!--<p class="login-form-desktop-subtitle">请登录您的账户</p> -->
                    </div>

                    <!-- 登录方式切换 -->
                     <n-tabs v-model:value="activeName" tab-class="custom-tab" justify-content="space-evenly" type="line" animated>
                     <!--<el-tabs v-model="activeName" class="demo-tabs" style="margin-top:1rem;padding-left:20px;padding-right:20px" >-->
                      <n-tab-pane tab="密码登录" name="first">
                            <n-space vertical style="margin-top:1.25rem;">
                                <n-input v-model:value="loginForm.username" size="large" round placeholder="请输入账号">
                                    <template #prefix>
                                        <n-icon :component="PersonOutline" size="large" style="margin-right: 8px;"/>
                                    </template>
                                </n-input>
                                </n-space>
                            <n-space vertical style="margin-top:1.25rem;">
                                <n-input v-model:value="loginForm.password" type="password" size="large" round show-password-on="click" placeholder="请输入密码"  >
                                    <template #prefix>
                                        <n-icon :component="LockClosedOutline" size="large" style="margin-right: 8px;"/>
                                    </template>
                                </n-input>
                            </n-space>
                        </n-tab-pane>
                        <n-tab-pane tab="验证码登录" name="second">
                             <n-space vertical style="margin-top:1.25rem;">
                                <n-input v-model:value="loginForm.username" size="large" round placeholder="请输入手机号">
                                    <template #prefix>
                                        <n-icon :component="CallSharp" size="large" style="margin-right: 8px;"/>
                                    </template>
                                </n-input>
                                </n-space>
                            <n-space vertical style="margin-top:1.25rem;">
                                <n-input v-model:value="loginForm.password" type="password" size="large" round show-password-on="click" placeholder="请输验证码"  >
                                    <template #prefix>
                                        <n-icon :component="KeySharp" size="large" style="margin-right: 8px;"/>
                                    </template>
                                </n-input>
                            </n-space>
                        </n-tab-pane>
                     </n-tabs>

                     <div class="button-container">
                        <button style="margin-top:1rem; margin-top:1.5rem;" type="submit" :disabled="isLoading" class="login-button" @click="handleLogin" >
                            <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
                            {{ isLoading ? '登录中...' : '登录' }}
                        </button>
                    </div>
                        <!-- 记住密码和忘记密码 -->
                        <!--  <div class="form-options">
                            <label class="remember-me">
                                <input
                                    v-model="rememberMe"
                                    type="checkbox"
                                    class="remember-checkbox"
                                />
                                <span class="remember-label">记住密码</span>
                            </label>
                            <button
                                type="button"
                                class="forgot-password"
                            >
                                忘记密码？
                            </button>
                        </div> -->
                        <!-- 登录按钮 -->
                    <!-- 其他登录方式 -->
                    <!-- <div class="alternative-login">
                        <p class="alternative-title">其他登录方式</p>
                        <div class="login-icons">
                            <button class="login-icon-button">
                                <i class="fab fa-weixin"></i>
                            </button>
                            <button class="login-icon-button">
                                <i class="fab fa-qq"></i>
                            </button>
                            <button class="login-icon-button">
                                <i class="fab fa-alipay"></i>
                            </button>
                        </div>
                    </div> -->

                    <!-- 底部备案信息 -->
                    <div class="footer">
                        <p>粤ICP备2024282373号-1</p>
                    </div>
                 </div>
            </div>
        </div>
</template>

<script setup >

import router from '@/scripts/router'
import {useUserStore} from '@/store/modules/userStore'

import {reactive, ref, getCurrentInstance} from 'vue'
const app = getCurrentInstance().appContext.app

const userStore = useUserStore()
const codeImg = ref()

const loginForm = reactive({
    username: '',
    password: '',
    code: ''
})

const show = ref(true);
const loginIng = ref(false)
const activeName = ref('first')
const loading = ref(false)
const codeEnable = ref()

function getCodeEnable() {
    $common.get('/system/config/getCodeEnable').then(res => {
        codeEnable.value = res.data
    })
}

getCodeEnable()

function refreshCode() {
    $common.get('/system/security/verification/code').then(res => {
        codeImg.value = 'data:image/png;base64,' + res.data.img
        loginForm.uuid = res.data.uuid
    })
}

refreshCode()

const tenantId = ref()
const tenantList = ref()
const showTenantSelect = ref(false)

function handleLogin() {
    console.info("handleLogin----》")
    if (!loginForm.username) {
        $message.warning('请输入用户名')
    } else if (!loginForm.password) {
        $message.warning('请输入密码')
    } else {
        loading.value = true
        $common.post('/system/security/tenant/by/username', { username: loginForm.username }).then(res => {
            if(res.data && res.data.length > 0){
                if(res.data.length === 1){
                    tenantId.value = res.data[0].tenantId
                    requestLogin()
                }else{
                    showTenantSelect.value = true
                    tenantList.value = res.data
                    loading.value = false
                }
            }else{
                $message.error("用户名或密码错误")
                loading.value = false
            }
        })
    }
}

function requestLogin(){
    loginForm.tenantId = tenantId.value || ''
    userStore.login(loginForm, app).then(token => {
        if (token) {
            router.push({path: '/home'})
        } else {
            loading.value = false
        }
    }).catch(() => {
        backLogin()
        refreshCode()
        loading.value = false
    })
}

function sendcodeLogin() {

}

function backLogin(){
    loading.value = false
    tenantList.value = []
    tenantId.value = ''
    showTenantSelect.value = false
}

</script>

<style lang="less" scoped>

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

:root {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --primary-text: #fff;
    --text-gray-600: #4b5563;
    --text-gray-700: #374151;
    --text-gray-900: #1f2937;
    --text-gray-500: #6b7280;
    --text-gray-400: #9ca3af;
    --text-gray-300: #d1d5db;
    --bg-gray-50: #f9fafb;
    --bg-gray-100: #f3f4f6;
    --bg-gray-200: #e5e7eb;
    --bg-blue-900: #1e3a8a;
    --bg-blue-600: #2563eb;
    --bg-blue-700: #1d4ed8;
    --bg-blue-900-60: rgba(30, 58, 138, 0.6);
    --border-gray-300: #d1d5db;
    --focus-ring: rgba(59, 130, 246, 0.5);
    --transition: all 0.3s ease;
}

body {
    background-color: var(--bg-gray-50);
    color: var(--text-gray-900);
    min-height: 100vh;
}

.login-container {
    display: flex;
    min-height: 100vh;
}

/* 左侧品牌区样式 */
.login-brand-container {
    display: none;
    position: relative;
    overflow: hidden;
    width: 55%;
}

.login-brand-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
}

.login-brand-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, var(--bg-blue-900-60), transparent);
}

.login-brand-content {
    position: relative;
    z-index: 10;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 3rem;
    color: white;
    height: 100%;
}

.login-brand-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.login-brand-subtitle {
    color: #bfdbfe;
    font-size: 1.125rem;
}

.login-brand-slogan {
    font-size: 2.25rem;
    font-weight: 700;
    line-height: 1.25;
    margin-bottom: 1rem;
}

.login-brand-description {
    color: #bfdbfe;
    font-size: 1.125rem;
    max-width: 28rem;
}

.login-brand-text {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 右侧表单区样式 */
.login-form-container {
    flex: 1;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

@media (min-width: 1024px) {
    .login-brand-container {
        display: flex;
    }
    .login-form-container {
        width: 40%;
    }
}

.login-form-wrapper {
    width: 100%;
    max-width: 22rem;
    background-color: #ffffff;
    border-radius: 30px;
    padding:16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login-form-mobile-header {
    text-align: center;
    margin-bottom: 2rem;
}

@media (min-width: 1024px) {
    .login-form-mobile-header {
        display: none;
    }
}

.login-form-mobile-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.login-form-mobile-subtitle {
    color: var(--text-gray-600);
}

.login-form-desktop-header {
    display: none;
    text-align: center;
    margin-bottom: 0rem;
}

@media (min-width: 1024px) {
    .login-form-desktop-header {
        display: block;
    }
}

.login-form-desktop-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.login-form-desktop-subtitle {
    color: var(--text-gray-600);
}

/* 登录类型切换 */
.login-type-toggle {
    display: flex;
    margin-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-gray-300);
}

.login-type-option {
    flex: 1;
    padding-bottom: 0.75rem;
    text-align: center;
    font-weight: 500;
    color: var(--text-gray-500);
    border-bottom: 2px solid transparent;
    cursor: pointer;
    background: none;
    border: none;
    font-size: 1rem;
    transition: var(--transition);
}

.login-type-option:hover {
    color: var(--primary-color);
}

.login-type-active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* 输入框组样式 */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    position: relative;
}

.input-verification-group {
    display: flex;
    gap: 0.75rem;
}

.input-verification {
    flex: 1;
}

.input-icon {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    padding-left: 0.75rem;
    display: flex;
    align-items: center;
    pointer-events: none;
    color: var(--text-gray-400);
    font-size: 0.875rem;
}

.login-input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 2.5rem;
    border: 1px solid var(--border-gray-300);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: var(--transition);
}

.login-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--focus-ring);
}

.input-toggle {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    padding-right: 0.75rem;
    display: flex;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
}

.eye-icon {
    color: var(--text-gray-400);
    font-size: 0.875rem;
}

.verification-button {
    padding: 0.75rem 1.5rem;
    background-color: var(--bg-gray-100);
    color: var(--text-gray-700);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.verification-button:hover {
    background-color: var(--bg-gray-200);
}

.verification-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 表单选项 */
.form-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.remember-checkbox {
    width: 1rem;
    height: 1rem;
    color: var(--primary-color);
    border-radius: 0.25rem;
    border: 1px solid var(--border-gray-300);
}

.remember-label {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-gray-600);
}

.forgot-password {
    font-size: 0.875rem;
    color: var(--primary-color);
    background: none;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.forgot-password:hover {
    color: var(--primary-hover);
}

.button-container {
    display: flex; /* 启用 flex 布局 */
    width: 100%; /* 确保父容器占满宽度 */

    button {
      color: #fff;
      background: #0066cc;
    }
}

/* 登录按钮 */
.login-button {
    flex: 1; /* 占据剩余空间 */
    margin-left: 20px;
    margin-right: 20px;
    padding: 0.75rem;
    background: linear-gradient(to right, var(--bg-blue-600), var(--bg-blue-700));
    color: var(--primary-text);
    border-radius: 0.5rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.login-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 其他登录方式 */
.alternative-login {
    margin-top: 2rem;
    text-align: center;
}

.alternative-title {
    font-size: 0.875rem;
    color: var(--text-gray-600);
    margin-bottom: 1rem;
}

.login-icons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.login-icon-button {
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--bg-gray-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.login-icon-button:hover {
    background-color: var(--bg-gray-200);
}

.login-icon-button .fa-weixin {
    color: #22c35e;
}

.login-icon-button .fa-qq {
    color: #3b82f6;
}

.login-icon-button .fa-alipay {
    color: #2563eb;
}

/* 底部信息 */
.footer {
    margin-top: 2rem;
    text-align: center;
    font-size: 0.75rem;
    color: var(--text-gray-400);
}

/* 响应式调整 */
@media (max-width: 1023px) {
    .login-brand-container {
        display: none;
    }
    
    .login-form-container {
        width: 100%;
    }
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

.fa-spinner.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

:deep(.n-input .n-input__input-el) {
    height: 40px !important;
    background-color:#fff !important;
}
</style>