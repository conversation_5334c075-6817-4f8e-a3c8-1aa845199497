<template>
  <div class="mb-x6design">
    <div class="mb-x6design-header">
      <div class="mb-x6design-title">{{ title || 'X6 设计器' }}</div>
      <div class="mb-x6design-actions">
        <n-button type="primary" size="small" @click="saveGraph">保存</n-button>
        <n-button size="small" @click="clearGraph">清空</n-button>
      </div>
    </div>
    <div class="mb-x6design-content">
      <!-- 画布区域 - 全屏背景 -->
      <div class="mb-x6design-graph" ref="graphContainer"></div>
      
      <!-- 左侧悬浮面板 -->
      <div class="mb-x6design-sidebar" ref="sidebarRef">
        <div class="mb-x6design-stencil" ref="stencilContainer"></div>
      </div>
      
      <!-- 右侧悬浮面板 -->
      <div class="mb-x6design-panel" ref="panelRef">
        <div class="mb-x6design-panel-title">{{ panelTitle || '属性设置' }}</div>
        <div v-if="selectedCell" class="mb-x6design-panel-content">
          <!-- 自定义属性面板插槽 -->
          <slot 
            name="property-panel" 
            :selected-cell="selectedCell" 
            :form-state="formState"
            :is-edge="isEdge"
            :line-style-options="lineStyleOptions"
            :update-cell-label="updateCellLabel"
            :update-cell-position="updateCellPosition"
            :update-cell-size="updateCellSize"
            :update-cell-style="updateCellStyle"
            :custom-data="customData"
            :on-create-custom-data="onCreateCustomData"
            :update-custom-data="updateCustomData"
          >
            <!-- 默认属性面板 -->
          <n-form label-placement="left" label-width="auto" :model="formState">
            <!-- 通用属性 -->
            <n-form-item label="ID">
                <n-input v-model="formState.id" disabled />
            </n-form-item>
            <n-form-item label="标签">
                <n-input v-model="formState.label" @blur="updateCellLabel" />
            </n-form-item>
            
            <!-- 节点特有属性 -->
            <template v-if="!isEdge">
              <n-divider>位置与大小</n-divider>
              <n-form-item label="X坐标">
                  <n-input-number v-model="formState.position.x" @blur="updateCellPosition" />
              </n-form-item>
              <n-form-item label="Y坐标">
                  <n-input-number v-model="formState.position.y" @blur="updateCellPosition" />
              </n-form-item>
              <n-form-item label="宽度">
                  <n-input-number v-model="formState.size.width" @blur="updateCellSize" />
              </n-form-item>
              <n-form-item label="高度">
                  <n-input-number v-model="formState.size.height" @blur="updateCellSize" />
              </n-form-item>
              
              <n-divider>样式</n-divider>
              <n-form-item label="填充颜色">
                  <n-color-picker v-model="formState.style.fill" @update:value="updateCellStyle" />
              </n-form-item>
              <n-form-item label="边框颜色">
                  <n-color-picker v-model="formState.style.stroke" @update:value="updateCellStyle" />
              </n-form-item>
              <n-form-item label="边框宽度">
                  <n-input-number v-model="formState.style.strokeWidth" @blur="updateCellStyle" />
              </n-form-item>
            </template>
            
            <!-- 边特有属性 -->
            <template v-else>
              <n-divider>样式</n-divider>
              <n-form-item label="线条颜色">
                  <n-color-picker v-model="formState.style.stroke" @update:value="updateCellStyle" />
              </n-form-item>
              <n-form-item label="线条宽度">
                  <n-input-number v-model="formState.style.strokeWidth" @blur="updateCellStyle" />
              </n-form-item>
              <n-form-item label="线条样式">
                  <n-select v-model="formState.style.strokeDasharray" @update:value="updateCellStyle" :options="lineStyleOptions" />
              </n-form-item>
            </template>
            
            <!-- 自定义数据 -->
            <n-divider>自定义数据</n-divider>
            <n-dynamic-input
                v-model="customData"
              :on-create="onCreateCustomData"
              @update:value="updateCustomData"
            >
              <template #create-button-default>
                添加数据项
              </template>
              <template #default="{ value }">
                <div style="display: flex; align-items: center; width: 100%">
                    <n-input v-model="value.key" style="margin-right: 12px;" placeholder="键" />
                    <n-input v-model="value.value" placeholder="值" />
                </div>
              </template>
            </n-dynamic-input>
          </n-form>
          </slot>
        </div>
        <div v-else class="mb-x6design-panel-empty">
          <p>请选择一个节点或连线来编辑属性</p>
        </div>
      </div>
      
      <!-- 独立的拖拽条 - 不影响面板布局 -->
      <div class="mb-x6design-resize-handle left" @mousedown="startLeftResize"></div>
      <div class="mb-x6design-resize-handle right" @mousedown="startRightResize"></div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import { Graph, Shape } from '@antv/x6'
import { Stencil } from '@antv/x6-plugin-stencil'
import { NButton, NForm, NFormItem, NInput, NInputNumber, NColorPicker, NDivider, NSelect, NDynamicInput } from 'naive-ui'
import { initGraph, exportGraphData } from './utils/graph-config'
import { getBaseNodeTemplates, createNode } from './utils/node-templates'
import { transformGraphDataToJSON } from './utils/data-transform'
import { registerSvgNode } from './utils/svg-loader'

export default {
  name: 'mb-x6design',
  components: {
    NButton,
    NForm,
    NFormItem,
    NInput,
    NInputNumber,
    NColorPicker,
    NDivider,
    NSelect,
    NDynamicInput
  },
  props: {
    // 设计器标题
    title: {
      type: String,
      default: 'X6 设计器'
    },
    // 属性面板标题
    panelTitle: {
      type: String,
      default: '属性设置'
    },
    // 节点模板
    nodeTemplates: {
      type: Array,
      default: () => getBaseNodeTemplates()
    },
    // Stencil配置
    stencilConfig: {
      type: Object,
      default: () => ({
        title: '节点',
        groups: [
          {
            name: 'basic',
            title: '基础节点',
            graphHeight: 250,
            layoutOptions: {
              columns: 2,
              columnWidth: 80,
              rowHeight: 80,
            },
          }
        ]
      })
    },
    // 是否使用小格子样式
    useGridStyle: {
      type: Boolean,
      default: false
    },
    // 默认数据
    defaultData: {
      type: Object,
      default: null
    }
  },
  emits: ['save', 'cell-select'],
  setup(props, { emit }) {
    // refs
    const graphContainer = ref(null)
    const stencilContainer = ref(null)
    const sidebarRef = ref(null)
    const panelRef = ref(null)
    
    // 图形和Stencil实例
    let graph = null
    let stencil = null
    
    // 选中的节点/边
    const selectedCell = ref(null)
    
    // 表单状态
    const formState = reactive({
      id: '',
      label: '',
      position: { x: 0, y: 0 },
      size: { width: 0, height: 0 },
      style: {
        fill: '#ffffff',
        stroke: '#5F95FF',
        strokeWidth: 1,
        strokeDasharray: ''
      }
    })
    
    // 自定义数据
    const customData = ref([])
    
    // 线条样式选项
    const lineStyleOptions = [
      { label: '实线', value: '' },
      { label: '虚线', value: '5,5' },
      { label: '点线', value: '1,5' },
      { label: '点划线', value: '10,5,1,5' }
    ]
    
    // 计算属性
    const isEdge = computed(() => {
      return selectedCell.value && selectedCell.value.isEdge()
    })
    
    // 初始化画布
    const initializeGraph = () => {
      if (!graphContainer.value) return
      
      // 延迟一点初始化，确保DOM已完全渲染
      setTimeout(() => {
        // 创建画布 - 获取父容器的完整尺寸
        const containerRect = graphContainer.value.parentElement.getBoundingClientRect()
        graph = initGraph(graphContainer.value, {
          width: containerRect.width,
          height: containerRect.height,
          background: {
            color: '#f8f9fa',
          }
        })
        
        // 使用内置image节点，无需注册自定义类型
        console.log('✅ 使用内置image节点类型')
        
        // 监听选择事件
        graph.on('cell:click', ({ cell }) => {
          selectCell(cell)
        })
        
        graph.on('blank:click', () => {
          selectCell(null)
        })
        
        // 如果启用小格子样式，添加连接桩悬浮显示逻辑
        if (props.useGridStyle) {
          // 鼠标进入节点时显示连接桩
          graph.on('node:mouseenter', ({ node }) => {
            const ports = node.getPorts()
            ports.forEach(port => {
              node.setPortProp(port.id, 'attrs/circle/opacity', 1)
            })
          })
          
          // 鼠标离开节点时隐藏连接桩
          graph.on('node:mouseleave', ({ node }) => {
            const ports = node.getPorts()
            ports.forEach(port => {
              node.setPortProp(port.id, 'attrs/circle/opacity', 0)
            })
          })
        }
        
        // 初始化Stencil
        initializeStencil()
        
        // 窗口大小变化，自动调整画布大小
        const resizeObserver = new ResizeObserver(() => {
          if (graphContainer.value && graph) {
            const containerRect = graphContainer.value.parentElement.getBoundingClientRect()
            graph.resize(containerRect.width, containerRect.height)
          }
        })
        
        resizeObserver.observe(graphContainer.value.parentElement)
        
        return resizeObserver
      }, 100)
    }
    
    // 初始化Stencil
    const initializeStencil = () => {
      if (!stencilContainer.value || !graph) return
      
      try {
        console.log('初始化Stencil', stencilContainer.value, '节点模板数量:', props.nodeTemplates.length)
        
        // 如果已经存在Stencil，先销毁
        if (stencil) {
          try {
            stencil.dispose()
          } catch (e) {
            console.log('销毁旧Stencil时出错，忽略:', e)
          }
        }
        
        // 获取当前sidebar宽度和高度
        const sidebarWidth = stencilContainer.value.parentElement.clientWidth || 260
        const sidebarHeight = stencilContainer.value.parentElement.clientHeight || 600
        const stencilWidth = sidebarWidth - 20 // 减去滚动条宽度和边距
        
        // 优化布局参数 - 修复列数计算和高度问题
        const getOptimalLayout = () => {
          // 节点尺寸：80x80px（与官网一致）
          const nodeSize = 80
          // 行高：90px（给标签留空间）
          const rowHeight = 90
          
          // 精确计算可用宽度 - 考虑实际的padding和边距
          const scrollbarWidth = 0   // X6容器通常不占用额外空间
          const containerMargin = 16  // 容器左右边距（8px * 2）
          const usableWidth = stencilWidth - scrollbarWidth - containerMargin
          
          // 更精确的列数计算：充分利用可用空间
          // 节点实际尺寸70x70，网格尺寸80x90，间距10x20
          let maxColumns = Math.max(1, Math.floor(usableWidth / nodeSize))
          
          // 验证计算结果，确保能充分利用空间
          const totalUsedWidth = maxColumns * nodeSize
          const remainingSpace = usableWidth - totalUsedWidth
          
          // 如果剩余空间足够再放一列，则增加一列
          if (remainingSpace >= nodeSize && maxColumns < 5) { // 限制最大5列，避免过度拥挤
            maxColumns = maxColumns + 1
          }
          
          console.log(`📐 优化布局计算:`)
          console.log(`  面板宽度: ${sidebarWidth}px`)
          console.log(`  可用宽度: ${usableWidth}px`)
          console.log(`  总使用宽度: ${totalUsedWidth}px`)
          console.log(`  剩余空间: ${remainingSpace}px`)
          console.log(`  最终列数: ${maxColumns}`)
          
          // 使用官网同样简洁的配置
          return {
            columns: maxColumns,      // 动态计算的列数
            columnWidth: nodeSize,    // 节点宽度（80px）
            rowHeight: rowHeight,     // 行高（90px）
            // 不设置其他复杂参数，让X6使用默认间距
          }
        }
        
        // 动态计算Stencil高度 - 修复DOM高度不足问题
        const searchBoxHeight = 40  // 搜索框高度（减少）
        const titleHeight = 30      // 标题栏高度（减少）
        const topPadding = 10       // 顶部边距
        const bottomPadding = 20    // 底部边距（减少，让内容区域更大）
        const reservedHeight = searchBoxHeight + titleHeight + topPadding + bottomPadding
        const dynamicHeight = Math.max(600, sidebarHeight - reservedHeight) // 增加最小高度到600px
        
        console.log(`📏 Stencil高度计算:`)
        console.log(`  容器总高度: ${sidebarHeight}px`)
        console.log(`  预留高度: ${reservedHeight}px (搜索${searchBoxHeight} + 标题${titleHeight} + 边距${topPadding + bottomPadding})`)
        console.log(`  可用高度: ${sidebarHeight - reservedHeight}px`)
        console.log(`  最终高度: ${dynamicHeight}px`)
        console.log(`  🎯 目标：确保DOM中的stencil-content有足够高度显示所有内容`)
        
        // 先创建节点并按组分类，以便计算每组的高度需求
        const nodesByGroup = {}
        
        // 初始化所有分组
        props.stencilConfig.groups.forEach(group => {
          nodesByGroup[group.name] = []
        })
        
        // 将节点按组分类
        props.nodeTemplates.forEach(template => {
          try {
            // 如果启用小格子样式，转换节点模板
            let nodeConfig = template
            if (props.useGridStyle) {
              nodeConfig = convertToGridStyle(template)
            }
            
            const node = graph.createNode(nodeConfig)
            
            // 根据节点ID或形状确定分组
            if (template.id.startsWith('route-svg-')) {
              nodesByGroup['svg']?.push(node)
            } else if (template.shape === 'image' && template.id.includes('-svg-')) {
              nodesByGroup['svg']?.push(node)
            } else {
              nodesByGroup['basic']?.push(node)
            }
          } catch (error) {
            console.error('创建节点失败:', template, error)
          }
        })
        
        // 动态计算每个分组的高度需求
        const calculateGroupHeight = (groupName, nodeCount) => {
          if (nodeCount === 0) return 80 // 空分组最小高度
          
          const layout = props.useGridStyle ? getOptimalLayout() : { columns: 2, rowHeight: 90 }
          const rows = Math.ceil(nodeCount / layout.columns)
          const groupTitleHeight = 40
          const groupPadding = 20  // 增加padding，确保内容完整显示
          const calculatedHeight = groupTitleHeight + (rows * layout.rowHeight) + groupPadding
          
          console.log(`📊 分组 ${groupName}: ${nodeCount}个节点, ${layout.columns}列, ${rows}行, 计算高度${calculatedHeight}px`)
          
          // 根据节点数量适当调整，但确保有足够空间
          if (nodeCount <= 4) {
            return Math.max(calculatedHeight, 150) // 少量节点最小高度150px
          } else {
            return Math.max(calculatedHeight, 250) // 多节点最小高度250px
          }
        }
        
        // 创建Stencil实例
        stencil = new Stencil({
          title: props.stencilConfig.title,
          target: graph,
          stencilGraphWidth: stencilWidth,
          stencilGraphHeight: dynamicHeight, // 使用动态计算的高度
          collapsable: false,
          groups: props.stencilConfig.groups.map(group => {
            const nodeCount = nodesByGroup[group.name]?.length || 0
            const groupHeight = calculateGroupHeight(group.name, nodeCount)
            
            return {
              ...group,
              graphHeight: groupHeight, // 根据节点数量动态设置分组高度
              layoutOptions: props.useGridStyle ? 
                getOptimalLayout() : 
                group.layoutOptions,
            }
          }),
          search: {
            placeholder: '搜索节点',
          },
        })
        
        // 清空容器内容
        stencilContainer.value.innerHTML = ''
        stencilContainer.value.appendChild(stencil.container)
        
        // Stencil渲染完成，让X6原生布局系统接管
        console.log('✅ Stencil初始化完成，使用X6原生布局系统')
        
        // 如果没有节点模板，跳过节点加载
        if (!props.nodeTemplates || props.nodeTemplates.length === 0) {
          console.log('没有节点模板，跳过节点加载')
          return
        }
        
        // 为每个分组加载节点
        props.stencilConfig.groups.forEach(group => {
          const groupNodes = nodesByGroup[group.name] || []
          console.log(`分组 ${group.name} 的节点数量:`, groupNodes.length)
          if (groupNodes.length > 0) {
            stencil.load(groupNodes, group.name)
          }
        })
        
        console.log('Stencil初始化完成', stencil)
      } catch (error) {
        console.error('初始化Stencil失败:', error)
      }
    }
    
    // 选择节点/边
    const selectCell = (cell) => {
      selectedCell.value = cell
      
      if (cell) {
        // 更新表单状态
        formState.id = cell.id
        
        if (cell.isNode()) {
          // 节点
          formState.label = cell.attr('label/text') || ''
          formState.position.x = cell.position().x
          formState.position.y = cell.position().y
          formState.size.width = cell.size().width
          formState.size.height = cell.size().height
          formState.style.fill = cell.attr('body/fill') || '#ffffff'
          formState.style.stroke = cell.attr('body/stroke') || '#5F95FF'
          formState.style.strokeWidth = cell.attr('body/strokeWidth') || 1
        } else {
          // 边
          formState.label = cell.getLabels()[0]?.attrs?.text?.text || ''
          formState.style.stroke = cell.attr('line/stroke') || '#5F95FF'
          formState.style.strokeWidth = cell.attr('line/strokeWidth') || 1
          formState.style.strokeDasharray = cell.attr('line/strokeDasharray') || ''
        }
        
        // 更新自定义数据
        updateCustomDataFromCell(cell)
      } else {
        // 清空自定义数据
        customData.value = []
      }
      
      // 向父组件发送选择事件
      emit('cell-select', cell)
    }
    
    // 从cell获取自定义数据
    const updateCustomDataFromCell = (cell) => {
      const data = cell.getData() || {}
      customData.value = Object.entries(data).map(([key, value]) => {
        return { key, value }
      })
    }
    
    // 更新节点标签
    const updateCellLabel = () => {
      if (!selectedCell.value) return
      
      if (selectedCell.value.isNode()) {
        selectedCell.value.attr('label/text', formState.label)
      } else {
        if (selectedCell.value.getLabels().length > 0) {
          selectedCell.value.setLabelAt(0, { attrs: { text: { text: formState.label } } })
        } else {
          selectedCell.value.appendLabel({ attrs: { text: { text: formState.label } } })
        }
      }
    }
    
    // 更新节点位置
    const updateCellPosition = () => {
      if (!selectedCell.value || !selectedCell.value.isNode()) return
      
      selectedCell.value.position(formState.position.x, formState.position.y)
    }
    
    // 更新节点大小
    const updateCellSize = () => {
      if (!selectedCell.value || !selectedCell.value.isNode()) return
      
      selectedCell.value.resize(formState.size.width, formState.size.height)
    }
    
    // 更新节点/边样式
    const updateCellStyle = () => {
      if (!selectedCell.value) return
      
      if (selectedCell.value.isNode()) {
        selectedCell.value.attr({
          body: {
            fill: formState.style.fill,
            stroke: formState.style.stroke,
            strokeWidth: formState.style.strokeWidth,
          }
        })
      } else {
        selectedCell.value.attr({
          line: {
            stroke: formState.style.stroke,
            strokeWidth: formState.style.strokeWidth,
            strokeDasharray: formState.style.strokeDasharray,
          }
        })
      }
    }
    
    // 创建自定义数据项
    const onCreateCustomData = () => {
      return { key: '', value: '' }
    }
    
    // 更新自定义数据
    const updateCustomData = () => {
      if (!selectedCell.value) return
      
      const data = {}
      customData.value.forEach(item => {
        if (item.key) {
          data[item.key] = item.value
        }
      })
      
      selectedCell.value.setData(data)
    }
    
    // 保存图形
    const saveGraph = () => {
      if (!graph) return
      
      const data = exportGraphData(graph)
      const jsonData = transformGraphDataToJSON(data)
      
      console.log('保存的图形数据:', jsonData)
      emit('save', jsonData)
    }
    
    // 清空图形
    const clearGraph = () => {
      if (!graph) return
      
      graph.clearCells()
      selectCell(null)
    }
    
    // 加载初始数据
    const loadInitialData = () => {
      if (!graph || !props.defaultData) return
      
      try {
        graph.fromJSON(props.defaultData)
      } catch (error) {
        console.error('加载初始数据失败:', error)
      }
    }
    
    // 转换为小格子样式 - 参考官网间距原理
    const convertToGridStyle = (template) => {
      const gridTemplate = { ...template }
      
      // 节点实际尺寸（小于网格尺寸以产生间距）
      gridTemplate.width = 70   // 70 < 80，产生10px左右间距
      gridTemplate.height = 70  // 70 < 90，产生20px上下间距
      
      // 根据原始形状调整样式
      if (template.shape === 'rect') {
        // 检查是否是SVG节点（包含image元素）
        const hasImage = template.markup && template.markup.some(item => item.tagName === 'image')
        
        if (hasImage) {
          // SVG节点的小格子样式
          gridTemplate.attrs = {
            ...template.attrs,
            body: {
              fill: '#ffffff',
              stroke: '#e0e0e0',
              strokeWidth: 1,
              rx: 6,
              ry: 6,
            },
            image: {
              ...template.attrs.image,
              width: 35,
              height: 35,
              x: 17.5, // 在70x70格子中居中
              y: 8,
            },
            label: {
              fontSize: 9,
              fill: '#666666',
              textAnchor: 'middle',
              refX: '50%',
              refY: '80%',
              textWrap: {
                width: 60,  // 适应70px宽度
                height: 15,
                ellipsis: true,
              },
            },
          }
        } else {
          // 普通矩形节点的小格子样式
          gridTemplate.attrs = {
            ...template.attrs,
            body: {
              fill: '#ffffff',
              stroke: '#e0e0e0',
              strokeWidth: 1,
              rx: 6,
              ry: 6,
            },
            label: {
              fontSize: 9,
              fill: '#666666',
              textWrap: {
                width: 60,  // 适应70px宽度
                height: 20,
                ellipsis: true,
              },
            },
          }
        }
      } else if (template.shape === 'circle') {
        gridTemplate.attrs = {
          ...template.attrs,
          body: {
            ...template.attrs.body,
            stroke: '#e0e0e0',
            strokeWidth: 1,
            r: 25,
          },
          label: {
            fontSize: 9,
            fill: template.attrs.body?.fill === '#ffffff' ? '#666666' : '#ffffff',
          },
        }
      }
      
      // 隐藏连接桩，只在悬浮时显示
      if (gridTemplate.ports) {
        gridTemplate.ports = {
          ...gridTemplate.ports,
          groups: Object.fromEntries(
            Object.entries(gridTemplate.ports.groups || {}).map(([key, group]) => [
              key,
              {
                ...group,
                attrs: {
                  ...group.attrs,
                  circle: {
                    ...group.attrs?.circle,
                    opacity: 0, // 默认隐藏
                  },
                },
              },
            ])
          ),
        }
      }
      
      return gridTemplate
    }
    
    // 拖拽调整左侧面板宽度
    const startLeftResize = (e) => {
      e.preventDefault()
      const startX = e.clientX
      const startWidth = sidebarRef.value.offsetWidth
      const resizeHandle = e.currentTarget // 获取正确的拖拽条元素
      
      const doDrag = (e) => {
        const newWidth = startWidth + e.clientX - startX
        if (newWidth >= 260 && newWidth <= 500) { // 限制最小和最大宽度
          // 确保元素存在才进行操作
          if (sidebarRef.value) {
            sidebarRef.value.style.width = newWidth + 'px'
          }
          // 同时更新拖拽条位置
          if (resizeHandle) {
            resizeHandle.style.left = newWidth + 'px'
          }
          console.log(`📏 左侧面板拖拽到新宽度: ${newWidth}px`)
        }
      }
      
      const stopDrag = () => {
        document.removeEventListener('mousemove', doDrag)
        document.removeEventListener('mouseup', stopDrag)
        document.body.style.cursor = ''
        
        // 拖拽结束后重新初始化Stencil以应用新的布局
        if (stencil && props.nodeTemplates.length > 0) {
          console.log('🔄 拖拽结束，重新初始化Stencil布局')
          setTimeout(() => {
            initializeStencil()
          }, 100)
        }
      }
      
      document.addEventListener('mousemove', doDrag)
      document.addEventListener('mouseup', stopDrag)
      document.body.style.cursor = 'col-resize'
    }
    
    // 拖拽调整右侧面板宽度
    const startRightResize = (e) => {
      e.preventDefault()
      const startX = e.clientX
      const startWidth = panelRef.value.offsetWidth
      const resizeHandle = e.currentTarget // 获取正确的拖拽条元素
      
      const doDrag = (e) => {
        const newWidth = startWidth - (e.clientX - startX) // 右侧面板拖拽方向相反
        if (newWidth >= 250 && newWidth <= 500) { // 限制最小和最大宽度
          // 确保元素存在才进行操作
          if (panelRef.value) {
            panelRef.value.style.width = newWidth + 'px'
          }
          // 同时更新拖拽条位置
          if (resizeHandle) {
            resizeHandle.style.right = newWidth + 'px'
          }
          console.log(`📏 右侧面板拖拽到新宽度: ${newWidth}px`)
        }
      }
      
      const stopDrag = () => {
        document.removeEventListener('mousemove', doDrag)
        document.removeEventListener('mouseup', stopDrag)
        document.body.style.cursor = ''
      }
      
      document.addEventListener('mousemove', doDrag)
      document.addEventListener('mouseup', stopDrag)
      document.body.style.cursor = 'col-resize'
    }
    
    // 暴露方法给父组件
    const getGraph = () => graph
    const getSelectedCell = () => selectedCell.value
    
    // 生命周期钩子
    onMounted(() => {
      const resizeObserver = initializeGraph()
      
      if (props.defaultData) {
        loadInitialData()
      }
      
      onBeforeUnmount(() => {
        if (resizeObserver) {
          resizeObserver.disconnect()
        }
        
        if (graph) {
          graph.dispose()
        }
      })
    })
    
    // 监听defaultData变化
    watch(() => props.defaultData, (newData) => {
      if (newData && graph) {
        graph.clearCells()
        loadInitialData()
      }
    })
    
    // 监听节点模板变化
    watch(() => props.nodeTemplates, (newTemplates) => {
      console.log('📦 节点模板变化，重新初始化Stencil，模板数量:', newTemplates.length)
      if (graph && newTemplates.length > 0) {
        // 延迟重新初始化，确保DOM更新完成
        setTimeout(() => {
          initializeStencil()
        }, 100)
      }
    }, { deep: true, immediate: false })
    
    return {
      graphContainer,
      stencilContainer,
        sidebarRef,
        panelRef,
      selectedCell,
      formState,
      customData,
      isEdge,
      lineStyleOptions,
      updateCellLabel,
      updateCellPosition,
      updateCellSize,
      updateCellStyle,
      onCreateCustomData,
      updateCustomData,
      saveGraph,
      clearGraph,
        startLeftResize,
        startRightResize,
        getGraph,
        getSelectedCell,
    }
  }
}
</script>

<style scoped>
.mb-x6design {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.mb-x6design-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f8f9fa;
  height: 50px;
  position: relative;
  z-index: 10;
}

.mb-x6design-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.mb-x6design-actions {
  display: flex;
  gap: 8px;
}

.mb-x6design-content {
  position: relative;
  flex: 1;
  overflow: hidden;
  height: calc(100% - 50px);
}

.mb-x6design-sidebar {
  position: absolute;
  top: 0;
  left: 0;
  width: 260px; /* 增加默认宽度以容纳三个小格子 */
  min-width: 260px;
  max-width: 500px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
  border-right: 1px solid #dcdfe6;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  overflow: auto;
  z-index: 1000;
}

.mb-x6design-resize-handle {
  position: absolute;
  width: 8px;
  height: 100%;
  background-color: #e6e6e6;
  cursor: col-resize;
  border: none;
  outline: none;
  user-select: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.mb-x6design-resize-handle.left {
  top: 0;
  left: 260px; /* 初始位置：左侧面板右边缘 */
  transition: none; /* 拖拽时禁用过渡动画 */
}

.mb-x6design-resize-handle.right {
  top: 0;
  right: 280px; /* 初始位置：右侧面板左边缘 */
  transition: none; /* 拖拽时禁用过渡动画 */
}

.mb-x6design-resize-handle:hover {
  background-color: #409eff;
}

/* 添加拖拽指示器 - 点状纹理 */
.mb-x6design-resize-handle::before {
  content: '⋮⋮';
  color: #999;
  font-size: 14px;
  line-height: 8px;
  letter-spacing: -2px;
  transition: color 0.2s ease;
  transform: rotate(90deg);
}

.mb-x6design-resize-handle:hover::before {
  color: #fff;
}

.mb-x6design-stencil {
  height: 100%;
  overflow: hidden; /* 禁用外层滚动，让X6内部处理滚动 */
}

/* X6 Stencil样式优化 - 解决双滚动条问题 */
.mb-x6design-stencil .x6-widget-stencil {
  height: 100% !important;
  overflow: hidden !important; /* 禁用外层滚动 */
}

.mb-x6design-stencil .x6-widget-stencil .x6-graph {
  background-color: #fafafa;
  overflow-y: auto !important; /* 只在graph层启用垂直滚动 */
  overflow-x: hidden !important; /* 禁用水平滚动 */
  padding-bottom: 20px !important; /* 底部预留空间，确保最后一行图标完整显示 */
}

/* 确保每个分组容器不产生额外滚动条 */
.mb-x6design-stencil .x6-widget-stencil-group {
  overflow: visible !important;
}

.mb-x6design-stencil .x6-widget-stencil-group .x6-widget-stencil-group-title {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
}

/* 确保搜索框容器不产生滚动条 */
.mb-x6design-stencil .x6-widget-stencil-search {
  overflow: hidden !important;
}

/* 保留必要的视觉效果 */
.mb-x6design-stencil .x6-widget-stencil .x6-graph-svg g[data-cell-id] {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  transition: all 0.2s ease;
}

.mb-x6design-stencil .x6-widget-stencil .x6-graph-svg g[data-cell-id]:hover {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transform: translateY(-1px);
}

.mb-x6design-graph {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f8f9fa;
}

.mb-x6design-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 280px;
  min-width: 250px;
  max-width: 500px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
  border-left: 1px solid #dcdfe6;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  overflow: auto;
  z-index: 1000;
}

.mb-x6design-panel-title {
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dcdfe6;
}

.mb-x6design-panel-content {
  padding: 16px;
}

.mb-x6design-panel-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
}
</style> 