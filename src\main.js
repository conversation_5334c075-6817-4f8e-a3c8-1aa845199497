import './styles/tailwind.css'
import '@/assets/css/common.css'
import '@/assets/css/common_g.css'
import '@/assets/css/magic-table.css'
import 'sv-print/dist/style.css'
import 'vite-plugin-svg-icons/register'
import {createApp} from 'vue'
import App from './App.vue'
import {
    setupNaive,
    setupNaiveDiscreteApi,
    setupDirectives,
    setupGlobalProperties,
    setupMonacoVolar,
    setupTheme,
    setupLayer,
    setupDayjs,
    setupElementPlus
} from '@/scripts/plugins'
import {setupRouter} from '@/scripts/router'
import {setupStore} from '@/store'
import {setupComponents} from '@/components'
import '@/scripts/compiler/magic-import'

const app = createApp(App)

async function start() {
    setupStore(app)
    setupNaive(app)
    setupNaiveDiscreteApi()
    setupDirectives(app)
    setupGlobalProperties(app)
    await setupRouter(app)
    await setupMonacoVolar()
    setupComponents(app)
    await setupTheme()
    setupLayer(app)
    setupDayjs()
    setupElementPlus(app)
    app.mount('#app')
}

void start()

