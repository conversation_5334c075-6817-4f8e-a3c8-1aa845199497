<template>
  <div class="mb-route-x6design">
    <mb-x6design 
      ref="baseDesignRef"
      title="路由设计器"
      panel-title="路由设置"
      :node-templates="routeNodeTemplates"
      :stencil-config="routeStencilConfig"
      :use-grid-style="true"
      :default-data="defaultData"
      @save="handleBaseSave"
      @cell-select="handleCellSelect"
    >
      <!-- 自定义路由属性面板 -->
      <template #property-panel="slotProps">
        <n-form label-placement="left" label-width="auto" :model="formState">
          <!-- 节点基本属性 -->
          <n-form-item label="ID">
            <n-input v-model="formState.id" disabled />
          </n-form-item>
          <n-form-item label="节点名称">
            <n-input v-model="formState.label" @blur="slotProps.updateCellLabel" />
          </n-form-item>
          
          <!-- 路由属性 -->
          <template v-if="!slotProps.isEdge">
            <n-divider>路由配置</n-divider>
            <n-form-item label="功能名称">
              <n-input v-model="formState.routeName" @blur="updateRouteInfo" />
            </n-form-item>
            <n-form-item label="路由地址">
              <n-input v-model="formState.routePath" @blur="updateRouteInfo" />
            </n-form-item>
            
            <!-- 位置与大小 -->
            <n-divider>位置与大小</n-divider>
            <n-form-item label="X坐标">
              <n-input-number v-model="formState.position.x" @blur="slotProps.updateCellPosition" />
            </n-form-item>
            <n-form-item label="Y坐标">
              <n-input-number v-model="formState.position.y" @blur="slotProps.updateCellPosition" />
            </n-form-item>
            <n-form-item label="宽度">
              <n-input-number v-model="formState.size.width" @blur="slotProps.updateCellSize" />
            </n-form-item>
            <n-form-item label="高度">
              <n-input-number v-model="formState.size.height" @blur="slotProps.updateCellSize" />
            </n-form-item>
            
            <!-- 样式 -->
            <n-divider>样式</n-divider>
            <n-form-item label="填充颜色">
              <n-color-picker v-model="formState.style.fill" @update:value="slotProps.updateCellStyle" />
            </n-form-item>
            <n-form-item label="边框颜色">
              <n-color-picker v-model="formState.style.stroke" @update:value="slotProps.updateCellStyle" />
            </n-form-item>
            <n-form-item label="边框宽度">
              <n-input-number v-model="formState.style.strokeWidth" @blur="slotProps.updateCellStyle" />
            </n-form-item>
          </template>
          
          <!-- 边属性 -->
          <template v-else>
            <n-form-item label="标签">
              <n-input v-model="formState.label" @blur="slotProps.updateCellLabel" />
            </n-form-item>
            
            <!-- 边样式 -->
            <n-divider>样式</n-divider>
            <n-form-item label="线条颜色">
              <n-color-picker v-model="formState.style.stroke" @update:value="slotProps.updateCellStyle" />
            </n-form-item>
            <n-form-item label="线条宽度">
              <n-input-number v-model="formState.style.strokeWidth" @blur="slotProps.updateCellStyle" />
            </n-form-item>
            <n-form-item label="线条样式">
              <n-select v-model="formState.style.strokeDasharray" @update:value="slotProps.updateCellStyle" :options="lineStyleOptions" />
            </n-form-item>
          </template>
        </n-form>
      </template>
    </mb-x6design>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { NForm, NFormItem, NInput, NInputNumber, NColorPicker, NDivider, NSelect } from 'naive-ui'
import MbX6design from './mb-x6design.vue'
import { getRouteNodeTemplates } from './utils/node-templates'
import { transformGraphDataToJSON, transformToRouteConfig } from './utils/data-transform'
import { loadSvgFiles, createSvgNodeTemplates } from './utils/svg-loader'

export default {
  name: 'mb-route-x6design',
  components: {
    NForm,
    NFormItem,
    NInput,
    NInputNumber,
    NColorPicker,
    NDivider,
    NSelect,
    MbX6design
  },
  props: {
    defaultData: {
      type: Object,
      default: null
    }
  },
  emits: ['save'],
  setup(props, { emit }) {
    // 基础设计器引用
    const baseDesignRef = ref(null)
    
    // 路由节点模板
    const routeNodeTemplates = ref([])
    
    // 路由Stencil配置
    const routeStencilConfig = {
      title: '路由节点',
      groups: [
        {
          name: 'basic',
          title: '基础节点',
          // 移除固定高度，让基础组件动态计算
        },
        {
          name: 'svg',
          title: 'SVG图形',
          // 移除固定高度，让基础组件动态计算
        }
      ]
    }
    
    // 当前选中的节点/边
    const selectedCell = ref(null)
    
    // 表单状态
    const formState = reactive({
      id: '',
      label: '',
      routeName: '',
      routePath: '',
      position: { x: 0, y: 0 },
      size: { width: 0, height: 0 },
      style: {
        fill: '#ffffff',
        stroke: '#5F95FF',
        strokeWidth: 1,
        strokeDasharray: ''
      }
    })
    
    // 线条样式选项
    const lineStyleOptions = [
      { label: '实线', value: '' },
      { label: '虚线', value: '5,5' },
      { label: '点线', value: '1,5' },
      { label: '点划线', value: '10,5,1,5' }
    ]
    
    // 计算属性
    const isEdge = computed(() => {
      return selectedCell.value && selectedCell.value.isEdge()
    })
    
    // 处理基础设计器的保存事件
    const handleBaseSave = (data) => {
      // 转换为路由配置
      const routeConfig = transformToRouteConfig(data)
      
      console.log('路由设计器保存数据:', data)
      console.log('路由配置:', routeConfig)
      
      emit('save', { 
        graphData: data, 
        routeConfig 
      })
    }
    
    // 处理节点选择事件
    const handleCellSelect = (cell) => {
      selectedCell.value = cell
      
      if (cell) {
        // 更新表单状态
        formState.id = cell.id
        
        if (cell.isNode()) {
          // 节点
          formState.label = cell.attr('label/text') || ''
          formState.position.x = cell.position().x
          formState.position.y = cell.position().y
          formState.size.width = cell.size().width
          formState.size.height = cell.size().height
          formState.style.fill = cell.attr('body/fill') || '#ffffff'
          formState.style.stroke = cell.attr('body/stroke') || '#5F95FF'
          formState.style.strokeWidth = cell.attr('body/strokeWidth') || 1
          
          // 获取路由信息
          const data = cell.getData() || {}
          formState.routeName = data.routeName || ''
          formState.routePath = data.routePath || ''
        } else {
          // 边
          formState.label = cell.getLabels()[0]?.attrs?.text?.text || ''
          formState.style.stroke = cell.attr('line/stroke') || '#5F95FF'
          formState.style.strokeWidth = cell.attr('line/strokeWidth') || 1
          formState.style.strokeDasharray = cell.attr('line/strokeDasharray') || ''
        }
      }
    }
    
    // 更新路由信息
    const updateRouteInfo = () => {
      if (!selectedCell.value || !selectedCell.value.isNode()) return
      
      const data = selectedCell.value.getData() || {}
      data.routeName = formState.routeName
      data.routePath = formState.routePath
      
      selectedCell.value.setData(data)
    }
    
    // 暴露保存方法
    const saveGraph = () => {
      if (baseDesignRef.value) {
        baseDesignRef.value.saveGraph()
      }
    }
    
    // 暴露清空方法
    const clearGraph = () => {
      if (baseDesignRef.value) {
        baseDesignRef.value.clearGraph()
      }
    }
    
    // 加载路由SVG图形
    const loadRouteSvgTemplates = async () => {
      try {
        console.log('开始加载路由SVG图形...')
        
        // 加载基础节点模板
        const basicTemplates = getRouteNodeTemplates()
        console.log('基础节点模板数量:', basicTemplates.length)
        
        // 先设置基础模板，确保界面有内容显示
        routeNodeTemplates.value = [...basicTemplates]
        
        // 加载SVG文件
        const svgFiles = await loadSvgFiles('x6-design-icons/route')
        console.log('加载的SVG文件:', Object.keys(svgFiles))
        
        if (Object.keys(svgFiles).length > 0) {
          // 创建SVG节点模板
          const svgTemplates = createSvgNodeTemplates(svgFiles, {
            width: 60,
            height: 60,
            groupName: 'route-svg',
            defaultData: {
              routeName: '',
              routePath: '',
            }
          })
          
          console.log('SVG节点模板数量:', svgTemplates.length)
          
          // 合并所有模板
          routeNodeTemplates.value = [...basicTemplates, ...svgTemplates]
        }
        
        console.log('路由节点模板加载完成，总数:', routeNodeTemplates.value.length)
        
        // 强制触发响应式更新
        await nextTick()
        
      } catch (error) {
        console.error('加载路由SVG模板失败:', error)
        // 如果加载失败，至少保证基础模板可用
        routeNodeTemplates.value = getRouteNodeTemplates()
      }
    }
    
    // 组件挂载时加载SVG模板
    onMounted(async () => {
      await loadRouteSvgTemplates()
    })
    
    return {
      baseDesignRef,
      routeNodeTemplates,
      routeStencilConfig,
      selectedCell,
      formState,
      isEdge,
      lineStyleOptions,
      handleBaseSave,
      handleCellSelect,
      updateRouteInfo,
      saveGraph,
      clearGraph,
    }
  }
}
</script>

<style scoped>
.mb-route-x6design {
  height: 100%;
  width: 100%;
}
</style>

<!-- 继承原有的X6样式修复 -->
<style>
/* 
解决问题：X6 Stencil插件生成的CSS样式影响Vue组件中表单标签的显示
原因：x6-widget-stencil容器可能设置了font-size: 0或其他影响文本显示的样式
解决方案：使用更强力的CSS选择器重置表单区域样式
*/

/* 强制重置整个右侧面板的字体样式 */
.mb-route-x6design .mb-x6design-panel,
.mb-route-x6design .mb-x6design-panel *,
.mb-route-x6design .mb-x6design-panel *::before,
.mb-route-x6design .mb-x6design-panel *::after {
  font-size: 12px !important;
  line-height: 1.6 !important;
  font-family: inherit !important;
  color: inherit !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 特别针对表单标签 */
.mb-route-x6design .mb-x6design-panel .n-form .n-form-item .n-form-item-label,
.mb-route-x6design .mb-x6design-panel .n-form .n-form-item .n-form-item-label *,
.mb-route-x6design .mb-x6design-panel .n-form-item-label,
.mb-route-x6design .mb-x6design-panel .n-form-item-label * {
  font-size: 12px !important;
  line-height: 24px !important;
  color: rgb(31, 34, 37) !important;
  font-family: inherit !important;
  visibility: visible !important;
  opacity: 1 !important;
  display: block !important;
  text-align: right !important;
}

/* 针对标签文本内容 */
.mb-route-x6design .mb-x6design-panel .n-form-item-label__text {
  font-size: 12px !important;
  line-height: 24px !important;
  color: rgb(31, 34, 37) !important;
  font-family: inherit !important;
  visibility: visible !important;
  opacity: 1 !important;
  display: inline !important;
}

/* 确保表单输入框也不受影响 */
.mb-route-x6design .mb-x6design-panel .n-input,
.mb-route-x6design .mb-x6design-panel .n-input *,
.mb-route-x6design .mb-x6design-panel .n-input-number,
.mb-route-x6design .mb-x6design-panel .n-input-number *,
.mb-route-x6design .mb-x6design-panel .n-color-picker,
.mb-route-x6design .mb-x6design-panel .n-color-picker *,
.mb-route-x6design .mb-x6design-panel .n-select,
.mb-route-x6design .mb-x6design-panel .n-select * {
  font-size: 12px !important;
  font-family: inherit !important;
  color: rgb(51, 54, 57) !important;
}

/* 确保分割线标题也显示正常 */
.mb-route-x6design .mb-x6design-panel .n-divider,
.mb-route-x6design .mb-x6design-panel .n-divider *,
.mb-route-x6design .mb-x6design-panel .n-divider .n-divider__title {
  font-size: 12px !important;
  color: rgb(31, 34, 37) !important;
  font-family: inherit !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 防止X6 CSS影响整个面板区域 */
.mb-route-x6design .mb-x6design-panel {
  font-size: 12px !important;
  line-height: 1.6 !important;
  font-family: inherit !important;
  isolation: isolate !important;
}

/* 更强力地隔离X6样式 */
.mb-route-x6design .x6-widget-stencil {
  contain: layout style !important;
  isolation: isolate !important;
}

/* 直接覆盖可能的X6全局样式 */
.mb-route-x6design .mb-x6design-panel label,
.mb-route-x6design .mb-x6design-panel span,
.mb-route-x6design .mb-x6design-panel div {
  font-size: inherit !important;
  line-height: inherit !important;
  color: inherit !important;
  visibility: visible !important;
  opacity: 1 !important;
}
</style> 