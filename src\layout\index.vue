<style lang="less">
.layout-container-erp{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .layout-header{
        height: 50px;
        background: #0869bd;
        display: flex;
        align-items: center;
        .logo-box{
            width: 120px;
            color: white;
            text-align: center;
            font-size: 22px;
            padding-left: 30px;
            box-sizing: border-box;
        }
        .mb-menus{
            display: flex;
            flex: 1;
            color: white;
            height: 100%;
            .mb-menu{
                padding: 0px 15px;
                height: 100%;
                display: flex;
                position: relative;
                align-items: center;
                &:hover{
                    cursor: pointer;
                }
                .title{
                    display: flex;
                    align-items: center;
                    font-size: 13px;
                }
                .sub-menus{
                    background: white;
                    padding: 20px 12px;
                    display: none;
                    box-shadow: 0 0 20px rgba(0,0,0,.2);
                    position: absolute;
                    top: 50px;
                    left: -20px;
                    min-width: 140px;
                    max-width: 840px;
                    z-index: 99999;
                    .sub-menu{
                        color: black;
                        .sub-type{
                            display: flex;
                            align-items: center;
                            color: #0869bd;
                            font-weight: 700;
                            .icon{
                                margin-left: 10px;
                                margin-right: 5px;
                                width: 20px;
                                height: 20px;
                                background: #ebf3f9;
                                border-radius: 20px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            }
                        }
                        .item-list{
                            display: flex;
                            margin-top: 12px;
                            .sub-items{
                                width: 144px;
                                .item{
                                    margin: 3px 0;
                                    padding: 2px 8px 2px 20px;
                                    line-height: 30px;
                                    height: 30px;
                                    font-size: 13px;
                                    color: rgba(0, 0, 0, .75);
                                    &:hover{
                                        background: #e3effd;
                                        cursor: pointer;
                                    }
                                }
                                &:not(:first-child){
                                    border-left: 1px solid #f2f2f2;
                                }
                            }

                        }
                        &:not(:first-child){
                            border-left: 1px solid #f2f2f2;
                        }
                    }
                }
                &:hover .sub-menus{
                    display: flex;
                }
                &:hover{
                    background: #07569b;
                }
            }
        }
        .header-right{
            width: 260px;
            height: 21px;
            display: flex;
            justify-content: end;
            padding-right: 10px;
            gap: 5px;
            color: white;
        }
    }
    .tabs-container{
        padding: 3px 5px 0px 5px;
        box-sizing: border-box;
        background: #eee;
        box-shadow: 0 -8px 10px -8px rgba(174,174,174,.35) inset;
    }
    .layout-content{
        flex: 1;
        overflow: auto;
        padding: 5px;
        box-sizing: border-box;
    }
}
</style>
<style scoped lang="less">
:deep(.n-tag){
    padding: 0 9.6px;
    margin-right: 5px;
    color: rgba(0, 0, 0, .87);
    font-size: 12px;
    background-color: #f6f6f6;
    border-radius: 2px 2px 0 0;
    box-shadow: 1px 0 3px rgba(174,174,174,.35);
    .n-base-close{
        color: #000;
        opacity: 0.3;
        &:hover{
            opacity: 0.4;
        }
    }
    &:hover{
        background: #fefefe;
    }
}
:deep(.n-tag.selected){
    box-shadow: 1px 0 3px rgba(174,174,174,.35), inset 0 2px 0 #0869bd;
    background: white;
}
</style>

<template>
    <div class="layout-container-erp">
        <div class="layout-header">
            <div class="logo-box">
                {{ $global.title }}
            </div>
            <div class="mb-menus">
                <div class="mb-menu" v-for="menu in menuOptions" :key="menu.id">
                    <div class="title">
                        {{ menu.title }}
                        <mb-icon icon="ChevronDownOutline" size="0.7em" />
                    </div>
                    <div class="sub-menus">
                        <div class="sub-menu" v-for="subMenu in menu.children" :key="subMenu.id">
                            <div class="sub-type">
                                <div class="icon">
                                    <mb-icon :icon="subMenu.icon" />
                                </div>
                                <div>
                                    {{ subMenu.title }}
                                </div>
                            </div>
                            <div class="item-list">
                                <div class="sub-items" v-for="(subItem, subIndex) in subMenu.itemList" :key="subIndex">
                                    <div class="item" @click="jump(item)" v-for="item in subItem.items" :key="item.id">
                                        {{ item.title }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <menu-search :menu-options="menuOptions" />
            <div class="header-right">
                <div>
                    {{ userStore.getInfo.username }}({{ userStore.getInfo.name }})
                </div>
                <div class="settings">
                    <n-dropdown :options="options" @select="handleSelect">
                        <mb-icon icon="SettingsOutline" color="white" size="1.5em" />
                    </n-dropdown>
                </div>
            </div>
        </div>
        <div class="tabs-container">
            <tabs />
        </div>
        <div class="layout-content">
            <component
                v-for="com in keepaliveIframes"
                :key="com.path"
                :is="IframeComponent"
                :url="common.getUrlType(com.meta.path) == 2 ? '/#' + com.meta.path : com.meta.path"
                v-show="com.path == $route.path"
            />
            <component
                v-for="com in keepaliveDynamicComponents"
                :key="com.path"
                :is="ShowComponent"
                :name="com.meta.componentName"
                v-show="com.path == $route.path"
            />
            <nested-router />
        </div>
    </div>
</template>

<script setup>
import {ref, watch, computed} from 'vue';
import tabs from './tabs.vue'
import NestedRouter from './nested-router.vue'
import MenuSearch from './menu-search.vue'
// 此页面template内 不能直接使用$common 需要这里导入 才能使用 原因未知
import common from '@/scripts/common'
import {useUserStore} from "@/store/modules/userStore"
import {useTabsStore} from "@/store/modules/tabsStore"
import IframeComponent from '@/views/common/iframe.vue'
import ShowComponent from '@/views/common/show-component.vue'
import { cloneDeep } from 'lodash-es'
import router from "@/scripts/router";

const tabsStore = useTabsStore()
const userStore = useUserStore()
const menuRef = ref()
const currentTab = tabsStore.getCurrentTab
const selectedKey = ref(currentTab)
selectMenu(currentTab)
watch(() => tabsStore.getCurrentTab, (key) => selectMenu(key))
// 单独处理 "iframe" 并且开启缓存的页面
const keepaliveIframes = computed(() => tabsStore.getTabs.filter(it => $common.filterIframeTabs(it)))
// 缓存“动态组件”
const keepaliveDynamicComponents = computed(() => tabsStore.getTabs.filter(it => it.meta.componentName && it.meta.keepAlive))

function selectMenu(key) {
    selectedKey.value = key
    menuRef.value?.showOption(key);
}

function jump(menu){
    if(menu.openMode === '1'){
        window.open($common.handlerUrlPage(menu.path))
    }else{
        router.push({
            path: menu.path
        })
    }
}

const menuOptions = ref(recursionRouters(userStore.getPermissionRouters))

function recursionRouters(children) {
    let itemNum = 6
    let menus = cloneDeep(children)
    menus.forEach(menu => {
        menu.children && menu.children.forEach(subMenu => {
            subMenu.itemList = []
            let items = []
            subMenu.children && subMenu.children.forEach((it, i) => {
                if(i%itemNum === 0){
                    items = []
                }
                items.push(it)
                if((i !== 0 && (i+itemNum+1)%itemNum === 0) || i === subMenu.children.length - 1){
                    subMenu.itemList.push({items: items})
                    items = []
                }
            })
        })
    })
    return menus
}

const options = ref([
    {
        label: "个人中心",
        key: "userCenter",
    },
    {
        label: "退出",
        key: "logout",
    },
])

function handleSelect(key) {
    switch (key) {
        case 'logout':
            userStore.logout()
            break;
        case 'userCenter':
            router.push({
                path: '/user-center'
            })
            break;
    }
}

</script>
