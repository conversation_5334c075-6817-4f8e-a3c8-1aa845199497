// SVG图形加载工具
import { Graph } from '@antv/x6'

/**
 * 动态加载指定目录下的所有SVG文件
 * @param {string} directory - 目录路径 (相对于src)
 * @returns {Promise<Object>} SVG文件映射对象
 */
export const loadSvgFiles = async (directory) => {
  try {
    console.log('开始加载SVG文件，目录:', directory)
    
    // 使用Vite的import.meta.glob动态加载SVG文件
    const svgModules = import.meta.glob('/src/x6-design-icons/**/*.svg', { 
      as: 'raw',
      eager: false 
    })
    
    console.log('找到的SVG模块:', Object.keys(svgModules))
    
    const svgFiles = {}
    const targetDir = `/src/${directory}`
    
    console.log('目标目录:', targetDir)
    
    // 筛选指定目录下的SVG文件
    for (const [path, loader] of Object.entries(svgModules)) {
      console.log('检查路径:', path)
      if (path.startsWith(targetDir)) {
        const fileName = path.substring(path.lastIndexOf('/') + 1, path.lastIndexOf('.'))
        console.log('加载SVG文件:', fileName)
        svgFiles[fileName] = await loader()
        console.log('SVG内容长度:', svgFiles[fileName].length)
      }
    }
    
    console.log('最终加载的SVG文件:', Object.keys(svgFiles))
    return svgFiles
  } catch (error) {
    console.error('加载SVG文件失败:', error)
    return {}
  }
}

/**
 * 将SVG内容转换为X6节点模板
 * @param {Object} svgFiles - SVG文件内容映射
 * @param {Object} options - 配置选项
 * @returns {Array} X6节点模板数组
 */
export const createSvgNodeTemplates = (svgFiles, options = {}) => {
  const {
    width = 60,
    height = 60,
    groupName = 'svg',
    defaultData = {}
  } = options
  
  const templates = []
  
  Object.entries(svgFiles).forEach(([fileName, svgContent]) => {
    console.log('📝 创建SVG节点模板:', fileName, 'SVG长度:', svgContent.length)
    
    // 提取SVG的viewBox或设置默认尺寸
    const viewBoxMatch = svgContent.match(/viewBox="([^"]*)"/)
    let svgWidth = width
    let svgHeight = height
    
    if (viewBoxMatch) {
      const viewBox = viewBoxMatch[1].split(' ')
      const vbWidth = parseFloat(viewBox[2])
      const vbHeight = parseFloat(viewBox[3])
      
      // 保持宽高比
      if (vbWidth && vbHeight) {
        const ratio = Math.min(width / vbWidth, height / vbHeight)
        svgWidth = vbWidth * ratio
        svgHeight = vbHeight * ratio
      }
    }
    
    // 清理SVG内容，移除可能的XML声明
    const cleanSvg = svgContent.replace(/^<\?xml[^>]*\?>/, '').trim()
    
    console.log('📝 创建image节点模板:', {
      id: `${groupName}-${fileName}`,
      shape: 'image',
      fileName,
      svgLength: cleanSvg.length
    })
    
    // 优化SVG内容
    const optimizedSvg = cleanSvg
      .replace(/<svg([^>]*)>/, '<svg$1 style="fill: #333333;">')
      .replace(/fill="currentColor"/g, 'fill="#333333"')
      .replace(/fill="none"/g, 'fill="#333333"')
    
    // 创建data URL
    const svgDataUrl = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(optimizedSvg)))
    
    console.log('📝 创建SVG data URL:', fileName, 'URL长度:', svgDataUrl.length)
    
    templates.push({
      id: `${groupName}-${fileName}`,
      shape: 'rect',
      width: 120, // 原始尺寸，会被基础组件转换
      height: 60,
      label: fileName,
      markup: [
        {
          tagName: 'rect',
          selector: 'body',
        },
        {
          tagName: 'image',
          selector: 'image',
        },
        {
          tagName: 'text',
          selector: 'label',
        },
      ],
      attrs: {
        body: {
          fill: '#f8f9fa',
          stroke: '#5F95FF',
          strokeWidth: 1,
          rx: 6,
          ry: 6,
        },
        image: {
          href: svgDataUrl,
          width: 40,
          height: 40,
          x: 40, // 居中
          y: 10,
          preserveAspectRatio: 'xMidYMid meet',
        },
        label: {
          fontSize: 12,
          fill: '#333333',
          textAnchor: 'middle',
          refX: '50%',
          refY: '100%',
          refY2: -5,
        },
      },
      data: {
        svgContent: cleanSvg,
        fileName: fileName,
        imageUrl: svgDataUrl,
        ...defaultData
      },
      ports: getDefaultPorts(),
    })
  })
  
  return templates
}

/**
 * 注册自定义SVG节点类型
 * @param {Graph} graph - X6图形实例
 */
export const registerSvgNode = (graph) => {
  if (!graph) return
  
  // 使用Graph类而不是实例来注册节点
  
  console.log('开始注册svg-node-27353节点类型')
  
  // 注册自定义SVG节点，使用try-catch处理重复注册
  try {
    Graph.registerNode('svg-node-27353', {
    inherit: 'rect',
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'image',
        selector: 'svg-image',
      },
      {
        tagName: 'text',
        selector: 'label',
      },
    ],
    attrs: {
      body: {
        fill: '#f8f9fa',
        stroke: '#5F95FF',
        strokeWidth: 2,
        rx: 8,
        ry: 8,
      },
      'svg-image': {
        width: '70%',
        height: '50%',
        x: '15%',
        y: '15%',
        preserveAspectRatio: 'xMidYMid meet',
      },
      label: {
        fontSize: 10,
        fill: '#333333',
        fontWeight: 'bold',
        textAnchor: 'middle',
        textVerticalAnchor: 'bottom',
        refX: '50%',
        refY: '100%',
        refY2: 4,
      },
    },
    // 初始化SVG内容
    setup() {
      console.log('🔍 setup函数被调用:', this.getData()?.fileName || 'unknown')
      
      // 先设置一个测试图像，确保基础功能正常
      const testSvg = '<svg width="24" height="24" viewBox="0 0 24 24" fill="#ff0000"><circle cx="12" cy="12" r="10"/></svg>'
      const testDataUrl = 'data:image/svg+xml;base64,' + btoa(testSvg)
      this.attr('svg-image/href', testDataUrl)
      console.log('🔍 设置测试图像完成')
      
      // 然后尝试设置真实的SVG
      const svgContent = this.getData()?.svgContent
      if (svgContent) {
        console.log('🔍 准备设置真实SVG图像:', this.getData()?.fileName)
        
        // 优化SVG内容
        const optimizedSvg = svgContent
          .replace(/<svg([^>]*)>/, '<svg$1 style="fill: #333333;">')
          .replace(/fill="currentColor"/g, 'fill="#333333"')
          .replace(/fill="none"/g, 'fill="#333333"')
        
        // 创建data URL
        const svgDataUrl = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(optimizedSvg)))
        
        // 延迟设置真实图像
        setTimeout(() => {
          this.attr('svg-image/href', svgDataUrl)
          console.log('🔍 设置真实SVG图像完成:', this.getData()?.fileName)
        }, 100)
      } else {
        console.log('🔍 没有找到SVG内容，保持测试图像')
      }
      
      // 监听节点添加事件
      this.on('added', () => {
        console.log('🔍 节点已添加到画布:', this.getData()?.fileName || 'unknown')
      })
    }
  })
  
  console.log('svg-node-27353节点类型注册成功')
  } catch (error) {
    if (error.message && error.message.includes('already registered')) {
      console.log('svg-node-27353节点类型已存在，跳过注册')
    } else {
      console.error('注册svg-node-27353节点类型失败:', error)
    }
  }
}

// 获取默认连接桩配置
const getDefaultPorts = () => {
  return {
    groups: {
      top: {
        position: 'top',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
      bottom: {
        position: 'bottom',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
      left: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
    },
    items: [
      {
        group: 'top',
      },
      {
        group: 'right',
      },
      {
        group: 'bottom',
      },
      {
        group: 'left',
      },
    ],
  }
} 