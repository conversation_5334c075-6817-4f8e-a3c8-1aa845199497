<template>
  <div class="mb-logicflow-toolbar">
    <div class="toolbar-left">
      <n-button-group>
        <n-button size="small" @click="undo" :disabled="!canUndo" title="撤销 (Ctrl+Z)">
          <template #icon>
            <n-icon><arrow-undo-outline /></n-icon>
          </template>
          撤销
        </n-button>
        <n-button size="small" @click="redo" :disabled="!canRedo" title="反撤销 (Ctrl+Y)">
          <template #icon>
            <n-icon><arrow-redo-outline /></n-icon>
          </template>
          反撤销
        </n-button>
      </n-button-group>

      <n-divider vertical />

      <n-button-group>
        <n-button size="small" @click="zoomIn">
          <template #icon>
            <n-icon><add-outline /></n-icon>
          </template>
          放大
        </n-button>
        <n-button size="small" @click="zoomOut">
          <template #icon>
            <n-icon><remove-outline /></n-icon>
          </template>
          缩小
        </n-button>
        <n-button size="small" @click="resetZoom">
          <template #icon>
            <n-icon><refresh-outline /></n-icon>
          </template>
          重置
        </n-button>
      </n-button-group>

      <n-divider vertical />

      <n-button size="small" @click="fitView">
        <template #icon>
          <n-icon><expand-outline /></n-icon>
        </template>
        适应画布
      </n-button>
    </div>

    <div class="toolbar-center">
      <span class="zoom-info">{{ Math.round(zoomLevel * 100) }}%</span>
    </div>

    <div class="toolbar-right">
      <n-button-group>
        <n-button size="small" @click="clearGraph" type="warning">
          <template #icon>
            <n-icon><trash-outline /></n-icon>
          </template>
          清空
        </n-button>
        <n-button size="small" @click="saveGraph" type="primary">
          <template #icon>
            <n-icon><save-outline /></n-icon>
          </template>
          保存
        </n-button>
      </n-button-group>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onUnmounted } from 'vue'
import { NButton, NButtonGroup, NDivider, NIcon } from 'naive-ui'
import { 
  ArrowUndoOutline, 
  ArrowRedoOutline, 
  AddOutline, 
  RemoveOutline, 
  RefreshOutline,
  ExpandOutline,
  TrashOutline,
  SaveOutline
} from '@vicons/ionicons5'

export default {
  name: 'MbLogicflowToolbar',
  components: {
    NButton,
    NButtonGroup,
    NDivider,
    NIcon,
    ArrowUndoOutline,
    ArrowRedoOutline,
    AddOutline,
    RemoveOutline,
    RefreshOutline,
    ExpandOutline,
    TrashOutline,
    SaveOutline
  },
  props: {
    logicFlow: {
      type: Object,
      default: null
    }
  },
  emits: ['save', 'clear'],
  setup(props, { emit }) {
    const zoomLevel = ref(1)
    const canUndo = ref(false)
    const canRedo = ref(false)
    
    // 用于存储键盘事件清理函数
    const keyboardCleanup = ref(null)

    /**
     * 撤销操作
     */
    const undo = () => {
      if (props.logicFlow) {
        props.logicFlow.undo()
        updateUndoRedoState()
      }
    }

    /**
     * 反撤销操作
     */
    const redo = () => {
      if (props.logicFlow) {
        props.logicFlow.redo()
        updateUndoRedoState()
      }
    }

    /**
     * 放大画布
     */
    const zoomIn = () => {
      if (props.logicFlow) {
        const currentZoom = props.logicFlow.getTransform().SCALE_X
        const newZoom = Math.min(currentZoom * 1.2, 3) // 最大3倍
        props.logicFlow.zoom(newZoom)
        zoomLevel.value = newZoom
      }
    }

    /**
     * 缩小画布
     */
    const zoomOut = () => {
      if (props.logicFlow) {
        const currentZoom = props.logicFlow.getTransform().SCALE_X
        const newZoom = Math.max(currentZoom / 1.2, 0.2) // 最小0.2倍
        props.logicFlow.zoom(newZoom)
        zoomLevel.value = newZoom
      }
    }

    /**
     * 重置缩放
     */
    const resetZoom = () => {
      if (props.logicFlow) {
        props.logicFlow.resetZoom()
        zoomLevel.value = 1
      }
    }

    /**
     * 适应画布
     */
    const fitView = () => {
      if (props.logicFlow) {
        props.logicFlow.fitView()
        // 获取适应后的缩放级别
        const transform = props.logicFlow.getTransform()
        zoomLevel.value = transform.SCALE_X
      }
    }

    /**
     * 清空画布
     */
    const clearGraph = () => {
      if (props.logicFlow) {
        props.logicFlow.clearData()
        emit('clear')
        // console.log('🗑️ 清空画布')
      }
    }

    /**
     * 保存图形数据
     */
    const saveGraph = () => {
      if (props.logicFlow) {
        const graphData = props.logicFlow.getGraphData()
        emit('save', graphData)
        // console.log('💾 保存图形数据:', graphData)
      }
    }

    /**
     * 更新撤销重做状态
     */
    const updateUndoRedoState = () => {
      // console.log('🔍 updateUndoRedoState 被调用')
      
      if (props.logicFlow) {
        // 根据LogicFlow官方文档，使用正确的API检查历史状态
        // LogicFlow没有直接的canUndo/canRedo方法，需要通过history:change事件获取状态
        // 这里先设置为基本状态，真正的状态会通过history:change事件更新
        
        // 尝试通过LogicFlow内部API获取历史状态（可能不稳定，主要依赖事件监听）
        try {
          // 检查是否有历史记录API
          if (props.logicFlow.history) {
            const history = props.logicFlow.history
            
            // LogicFlow初始化时可能会产生1个初始状态的历史记录
            // 所以我们需要检查是否有用户操作的历史记录（通常undos.length > 1才表示有用户操作）
            const undosLength = history.undos?.length || 0
            const redosLength = history.redos?.length || 0
            
            // 更智能的判断：
            // - 如果undos只有1个，可能是初始状态，不算用户操作
            // - 如果undos有2个或以上，说明有用户操作
            const newCanUndo = undosLength > 1
            const newCanRedo = redosLength > 0
            
            // console.log('🔄 通过API获取历史状态:', {
            //   'history对象': history,
            //   'undos数组': history.undos,
            //   'redos数组': history.redos,
            //   'undos长度': undosLength,
            //   'redos长度': redosLength,
            //   '原始判断canUndo': undosLength > 0,
            //   '智能判断canUndo': newCanUndo,
            //   '计算出的canRedo': newCanRedo,
            //   '当前canUndo': canUndo.value,
            //   '当前canRedo': canRedo.value
            // })
            
            canUndo.value = newCanUndo
            canRedo.value = newCanRedo
          } else {
            // console.log('⚠️ LogicFlow没有history属性，设置初始状态为false')
            canUndo.value = false
            canRedo.value = false
          }
        } catch (error) {
          // console.warn('⚠️ 获取历史状态失败:', error)
          canUndo.value = false
          canRedo.value = false
        }
      } else {
        // console.log('⚠️ LogicFlow实例不存在，重置状态')
        canUndo.value = false
        canRedo.value = false
      }
      
      // console.log('✅ updateUndoRedoState 完成，最终状态:', {
      //   canUndo: canUndo.value,
      //   canRedo: canRedo.value
      // })
    }

    /**
     * 初始化工具栏状态
     */
    const initToolbar = () => {
      if (props.logicFlow) {
        // 监听缩放变化
        props.logicFlow.on('graph:transform', (data) => {
          zoomLevel.value = data.transform.SCALE_X
        })

        // 监听历史变化，更新撤销重做状态
        // 根据LogicFlow官方文档：history:change事件返回包含undoAble和redoAble的数据对象
        props.logicFlow.on('history:change', (eventData) => {
          // console.log('📈 ===== history:change事件触发 =====')
          // console.log('📈 原始事件数据:', eventData)
          // console.log('📈 事件数据类型:', typeof eventData)
          
          // 根据实际日志，事件数据被包装在data属性中
          const data = eventData?.data || eventData
          
          // console.log('📈 解析后的data:', data)
          // console.log('📈 事件数据详情:', {
          //   '原始eventData': eventData,
          //   '解析后的data': data,
          //   '是否为对象': typeof data === 'object',
          //   '是否为null': data === null,
          //   'undoAble属性': data?.undoAble,
          //   'redoAble属性': data?.redoAble,
          //   'undos属性': data?.undos,
          //   'redos属性': data?.redos,
          //   '所有属性': Object.keys(data || {})
          // })
          
          // 根据官方文档，data包含undoAble和redoAble属性
          if (data && typeof data === 'object') {
            const oldCanUndo = canUndo.value
            const oldCanRedo = canRedo.value
            
            // 尝试多种可能的属性名
            const newCanUndo = data.undoAble !== undefined ? data.undoAble : 
                              data.canUndo !== undefined ? data.canUndo :
                              (data.undos && data.undos.length > 0)
            
            const newCanRedo = data.redoAble !== undefined ? data.redoAble :
                              data.canRedo !== undefined ? data.canRedo :
                              (data.redos && data.redos.length > 0)
            
            canUndo.value = newCanUndo || false
            canRedo.value = newCanRedo || false
            
            // console.log('🔄 通过history:change事件更新撤销重做状态:', {
            //   '事件中的undoAble': data.undoAble,
            //   '事件中的redoAble': data.redoAble,
            //   '事件中的canUndo': data.canUndo,
            //   '事件中的canRedo': data.canRedo,
            //   '事件中的undos': data.undos,
            //   '事件中的redos': data.redos,
            //   '计算出的newCanUndo': newCanUndo,
            //   '计算出的newCanRedo': newCanRedo,
            //   '之前的canUndo': oldCanUndo,
            //   '之前的canRedo': oldCanRedo,
            //   '更新后的canUndo': canUndo.value,
            //   '更新后的canRedo': canRedo.value,
            //   '状态是否改变': oldCanUndo !== canUndo.value || oldCanRedo !== canRedo.value
            // })
          } else {
            // 如果事件数据格式不正确，回退到API查询
            // console.warn('⚠️ history:change事件数据格式异常，数据:', eventData)
            // console.log('🔄 回退到API查询方式')
            updateUndoRedoState()
          }
          
          // console.log('📈 ===== history:change事件处理完成 =====')
        })

        // 添加键盘快捷键支持
        const handleKeyDown = (event) => {
          // 检查是否按下了Ctrl键（Windows/Linux）或Cmd键（Mac）
          const isCtrlOrCmd = event.ctrlKey || event.metaKey
          
          if (isCtrlOrCmd && !event.shiftKey && !event.altKey) {
            switch (event.key.toLowerCase()) {
              case 'z':
                event.preventDefault()
                if (canUndo.value) {
                  // console.log('⌨️ 键盘快捷键触发撤销操作')
                  undo()
                }
                break
              case 'y':
                event.preventDefault()
                if (canRedo.value) {
                  // console.log('⌨️ 键盘快捷键触发反撤销操作')
                  redo()
                }
                break
            }
          }
          
          // 也支持 Ctrl+Shift+Z 作为反撤销的替代快捷键
          if (isCtrlOrCmd && event.shiftKey && event.key.toLowerCase() === 'z') {
            event.preventDefault()
            if (canRedo.value) {
              // console.log('⌨️ 键盘快捷键触发反撤销操作 (Ctrl+Shift+Z)')
              redo()
            }
          }
        }

        // 添加键盘事件监听器
        document.addEventListener('keydown', handleKeyDown)
        
        // 保存移除监听器的函数
        keyboardCleanup.value = () => {
          document.removeEventListener('keydown', handleKeyDown)
          // console.log('🧹 键盘事件监听器已清理')
        }

        // console.log('✅ 工具栏初始化完成，撤销重做状态监听已启用，键盘快捷键已激活')
      }
    }

    // 监听logicFlow变化，初始化工具栏
    const unwatchLogicFlow = computed(() => props.logicFlow)
    
    // 使用watch监听logicFlow的变化，当logicFlow实例化后执行初始化
    watch(() => props.logicFlow, (newLogicFlow, oldLogicFlow) => {
      // console.log('🎯 ===== LogicFlow实例变化监听 =====')
      // console.log('🎯 旧实例:', oldLogicFlow)
      // console.log('🎯 新实例:', newLogicFlow)
      // console.log('🎯 当前按钮状态 - canUndo:', canUndo.value, 'canRedo:', canRedo.value)
      
      if (newLogicFlow) {
        // console.log('�� LogicFlow实例已传入工具栏，开始初始化')
        // console.log('🎯 LogicFlow实例详情:', {
        //   id: props.logicFlow.graphModel?.id,
        //   type: typeof props.logicFlow,
        //   methods: Object.getOwnPropertyNames(props.logicFlow),
        //   hasHistory: !!props.logicFlow.history,
        //   hasUndo: typeof props.logicFlow.undo === 'function',
        //   hasRedo: typeof props.logicFlow.redo === 'function'
        // })
        initToolbar()
      } else {
        // console.log('⚠️ LogicFlow实例为空，重置工具栏状态')
        canUndo.value = false
        canRedo.value = false
        zoomLevel.value = 1
      }
      
      // console.log('🎯 ===== LogicFlow实例变化处理完成 =====')
    }, { immediate: true })

    // 组件卸载时的清理逻辑
    onUnmounted(() => {
      // console.log('🧹 工具栏组件卸载，开始清理资源')
      if (keyboardCleanup.value) {
        keyboardCleanup.value()
        keyboardCleanup.value = null
      }
    })

    return {
      zoomLevel,
      canUndo,
      canRedo,
      undo,
      redo,
      zoomIn,
      zoomOut,
      resetZoom,
      fitView,
      clearGraph,
      saveGraph,
      initToolbar
    }
  }
}
</script>

<style scoped>
.mb-logicflow-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-center {
  display: flex;
  align-items: center;
}

.zoom-info {
  font-size: 12px;
  color: #595959;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  min-width: 50px;
  text-align: center;
}

:deep(.n-divider) {
  margin: 0 8px;
}
</style> 