<template>
    <n-config-provider
        :locale="zhCN"
        :date-locale="dateZhCN"
        v-bind="configProvider"
    >
        <n-message-provider>
            <n-dialog-provider>
                <n-loading-bar-provider>
                    <router-view/>
                </n-loading-bar-provider>
            </n-dialog-provider>
        </n-message-provider>
    </n-config-provider>
</template>

<script setup>
import { reactive } from 'vue'
import {NConfigProvider, zhCN, dateZhCN} from 'naive-ui'
import global from '@/scripts/global'
const configProvider = reactive({})
configProvider.themeOverrides = global.selectTheme.themeOverrides

</script>

<style lang="less">
@import 'styles/index.less';
.n-config-provider{
    height: 100%;
}
</style>
