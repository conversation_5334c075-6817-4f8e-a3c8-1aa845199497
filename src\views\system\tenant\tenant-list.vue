<template>
    <div class="mb-list">
        <div class="mb-search">
            <mb-search :where="tableOptions.where" @search="reloadTable"/>
        </div>
        <div class="mb-toolbar">
            <n-space>
                <n-button :size="$global.uiSize.value" v-permission="'tenant:save'" type="primary" @click="handleCreate">
                    <mb-icon icon="AddOutline" />
                    添加租户
                </n-button>
            </n-space>
        </div>
        <div class="mb-table">
            <mb-table ref="table" v-bind="tableOptions"/>
        </div>
        <mb-modal ref="formDialog" :title="dialogTitle" width="500px" @confirm="save($event)">
            <n-form :size="$global.uiSize.value" ref="dataForm" :rules="rules" :model="temp" label-placement="top" label-width="150px">
                <n-grid :cols="12">
                    <n-gi :span="12">
                        <n-form-item label="租户名称" path="name">
                            <mb-input :size="$global.uiSize.value" v-model="temp.name"/>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12" v-if="dialogTitle === '添加'">
                    <n-gi :span="12">
                        <n-form-item path="sourceTenantId">
                            <template #label>
                                源租户
                                <n-tooltip trigger="hover">
                                    <template #trigger>
                                        <mb-icon icon="QuestionCircleFilled" color="#4b6fa7" />
                                    </template>
                                    从源租户复制到新租户
                                </n-tooltip>
                            </template>
                            <mb-select v-model="temp.sourceTenantId" url="/system/user/get/tenants" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi :span="12">
                        <n-form-item label="有效期" path="validDate">
                            <mb-date type="date" v-model="temp.validDate" :shortcuts="shortcuts" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
            </n-form>
        </mb-modal>
    </div>
</template>

<script setup>

import {ref, reactive, nextTick} from 'vue'

const shortcuts = reactive({
    "1个月": () => $dayjs().add(1, 'month').add(1, 'day').toDate().getTime(),
    "3个月": () => $dayjs().add(3, 'month').add(1, 'day').toDate().getTime(),
    "6个月": () => $dayjs().add(6, 'month').add(1, 'day').toDate().getTime(),
    "1年": () => $dayjs().add(1, 'year').add(1, 'day').toDate().getTime(),
    "2年": () => $dayjs().add(2, 'year').add(1, 'day').toDate().getTime(),
    "3年": () => $dayjs().add(3, 'year').add(1, 'day').toDate().getTime(),
    "4年": () => $dayjs().add(4, 'year').add(1, 'day').toDate().getTime(),
    "5年": () => $dayjs().add(5, 'year').add(1, 'day').toDate().getTime(),
    "永久": () => $dayjs('2099-01-01').toDate().getTime()
})
const dialogTitle = ref('')
const formDialog = ref()
const tableOptions = reactive({
    id: 'tenant-list',
    url: '/system/tenant/list',
    page: true,
    where: {
        name: {
            label: '租户名称'
        }
    },
    cols: [
        {
            field: 'name',
            label: '租户名称'
        },
        {
            field: 'tenantId',
            label: '租户ID'
        },
        {
            field: 'sourceTenantName',
            label: '源租户名称'
        },
        {
            field: 'magicDatasourceFileId',
            label: '初始化数据库',
            templet(row){
                return row.magicDatasourceFileId ? '<span style="color: green">是</span>' : '<span style="color: red">否</span>'
            }
        },
        {
            field: 'magicGroupFileId',
            label: '初始化API',
            templet(row){
                return row.magicGroupFileId ? '<span style="color: green">是</span>' : '<span style="color: red">否</span>'
            }
        },
        {
            field: 'validDate',
            label: '有效期',
            width: 200,
            templet: (row) => {
                if(row.validDateDiff < 0){
                    return `<font color="red" title="已过期">${row.validDate}</font>`
                }
                return row.validDate
            }
        },
        {
            field: 'createDate',
            label: '创建时间',
            width: 200
        },
        {
            label: '操作',
            type: 'buttons',
            width: 220,
            fixed: 'right',
            buttons: [
                {
                    permission: 'tenant:init',
                    label: '初始化租户',
                    link: true,
                    click: (row) => {
                        let deleteWarning = $common.warning(row.isInitDatabase > 0 ? '此租户已经初始化，确定重新初始化吗？' : '确定初始化该租户吗？', () => {
                            deleteWarning.loading = true
                            return new Promise((resolve) => {
                                $common.post('/system/tenant/init', { id: row.id }).then(() => {
                                    $message.success('租户初始化成功')
                                    row.isInitDatabase = 1
                                    resolve()
                                })
                            })
                        }, {
                            maskClosable: false,
                            closeOnEsc: false
                        })
                    }
                },
                {
                    permission: 'tenant:save',
                    label: '修改',
                    link: true,
                    click: (row) => {
                        handleUpdate(row)
                    }
                },
                {
                    permission: 'tenant:delete',
                    label: '删除',
                    link: true,
                    if(row){
                        return row.tenantId != 'tenant1'
                    },
                    click: (row) => {
                        $common.error('此操作会删除租户数据源、数据库、api接口，并且无法恢复！确定要删除吗？', () => {
                            $common.delete('/system/tenant/delete', { id: row.id }).then(() => {
                                $message.success('删除成功')
                                reloadTable()
                            })
                        })
                    }
                }
            ]
        }
    ]
})
const temp = ref(getTemp())
const dialogStatus = ref('')
const rules = reactive({
    name: {required: true, message: '请输入租户名称', trigger: 'blur'},
    sourceTenantId: {required: true, message: '请选择源租户', trigger: 'blur'},
    validDate: {required: true, message: '请选择有效期', trigger: 'blur'}
})
const table = ref()
const dataForm = ref()

function getTemp() {
    return {
        id: '',
        name: '',
        sourceTenantId: '',
        validDate: null
    }
}

function reloadTable() {
    table.value.reload()
}

function handleCreate() {
    temp.value = getTemp()
    dialogTitle.value = '添加'
    formDialog.value.show()
    nextTick(() => {
        dataForm.value.restoreValidation()
    })
}

function save(d) {
    dataForm.value.validate((errors) => {
        if (!errors) {
            d.loading()
            $common.post('/system/tenant/save', temp.value).then(() => {
                d.hideLoading()
                d.hide()
                $message.success((dialogStatus.value === 'create' ? '创建' : '修改') + '成功')
                reloadTable()
            }).catch(() => d.hideLoading())
        }
    })
}

function handleUpdate(row) {
    $common.objAssign(temp.value, row)
    dialogTitle.value = '修改'
    formDialog.value.show()
    nextTick(() => {
        dataForm.value.restoreValidation()
    })
}

</script>
