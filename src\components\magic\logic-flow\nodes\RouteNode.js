import { h, RectNodeModel, RectNode } from '@logicflow/core'

/**
 * 路由节点模型类 - 支持多种几何形状
 */
class RouteNodeModel extends RectNodeModel {
  initNodeData(data) {
    super.initNodeData(data)
    
    // 设置节点默认尺寸
    this.width = data.properties?.width || 120
    this.height = data.properties?.height || 80
    
    // 设置默认属性 - 确保所有必需的属性都有默认值
    this.properties = {
      functionName: data.properties?.functionName || '新功能',
      routePath: data.properties?.routePath || '/new-route',
      iconSvg: data.properties?.iconSvg || '',
      description: data.properties?.description || '请输入节点描述',
      shapeType: data.properties?.shapeType || 'rectangle', // 新增：形状类型
      backgroundColor: data.properties?.backgroundColor || '#f3f0ff',
      iconColor: data.properties?.iconColor || '#ffffff',
      textColor: data.properties?.textColor || '#333333',
      ...this.properties,
      ...data.properties  // 确保传入的属性能覆盖默认值
    }
    
    // 重要：初始化时就设置文本的正确位置
    const textPosition = this.getTextPosition()
    this.text = {
      ...this.text,
      value: this.properties.functionName,
      x: textPosition.x,
      y: textPosition.y,
      editable: true
    }
    
    console.log('🏗️ 节点初始化完成:', {
      节点ID: this.id,
      形状类型: this.properties.shapeType,
      尺寸: `${this.width}x${this.height}`,
      节点属性: this.properties
    })
  }
  
  /**
   * 获取文本位置 - 根据形状类型决定文字位置
   */
  getTextPosition() {
    const properties = this.getProperties()
    
    if (properties.shapeType === 'card') {
      // card类型：文字在右边白色文字区域的中心
      const margin = 3 // 整体边距
      const iconAreaSize = this.height - margin * 2
      const textMargin = 2 // 文字区域的右上下边距（一致）
      const textAreaX = this.x - this.width / 2 + iconAreaSize + margin // 直接贴着图标区域
      const textAreaWidth = this.width - (iconAreaSize + margin) - textMargin // 减去图标区域占用的总宽度和右边距
      return {
        x: textAreaX + textAreaWidth / 2, // 白色文字区域中心x
        y: this.y // 垂直居中
      }
    } else {
      // 其他形状：图标+文字整体垂直居中
      return {
        x: this.x,
        y: this.y + 18
      }
    }
  }
  
  /**
   * 获取图标位置 - 根据形状类型决定图标位置
   */
  getIconPosition() {
    const properties = this.getProperties()
    
    if (properties.shapeType === 'card') {
      // card类型：图标在左边背景色区域的中心
      const margin = 3 // 整体边距
      const iconAreaSize = this.height - margin * 2
      return {
        x: this.x - this.width / 2 + margin + iconAreaSize / 2, // 左边背景色区域中心
        y: this.y
      }
    } else {
      // 其他形状：图标在整体上方，使图标+文字整体垂直居中
      return {
        x: this.x,
        y: this.y - 10
      }
    }
  }
  
  /**
   * 设置节点样式 - 根据形状类型返回不同样式
   */
  getNodeStyle() {
    const style = super.getNodeStyle()
    const properties = this.getProperties()
    const isHovered = properties.hovered
    
    const baseStyle = {
      ...style,
      fill: properties.backgroundColor || '#ffffff',
      stroke: isHovered ? '#1890ff' : (properties.borderColor || '#d9d9d9'),
      strokeWidth: isHovered ? 3 : (properties.borderWidth || 2),
      transition: 'stroke 0.2s ease, stroke-width 0.2s ease'
    }
    
    // 根据形状类型添加特定样式
    switch (properties.shapeType) {
      case 'circle':
      case 'ellipse':
        return {
          ...baseStyle,
          rx: this.width / 2,
          ry: this.height / 2
        }
      case 'square':
      case 'rectangle':
        return {
          ...baseStyle,
          rx: 8,
          ry: 8
        }
      case 'diamond':
        return {
          ...baseStyle,
          rx: 0,
          ry: 0
        }
      case 'card':
        return {
          ...baseStyle,
          // card类型的样式由getShape方法中的复合元素控制
          // 这里只是为了保持一致性，实际不会使用
          fill: 'none',
          stroke: 'none'
        }
      default:
        return {
          ...baseStyle,
          rx: 8,
          ry: 8
        }
    }
  }

  /**
   * 设置文字样式 - 确保文本位置正确，动态读取properties
   */
  getTextStyle() {
    const style = super.getTextStyle()
    const properties = this.getProperties()
    const textPosition = this.getTextPosition()
    
    return {
      ...style,
      fontSize: properties.fontSize || 12,
      fill: properties.textColor || properties.fontColor || '#333333',
      textAnchor: 'middle',
      dominantBaseline: 'middle',
      x: textPosition.x,
      y: textPosition.y
    }
  }
  
  /**
   * 移动节点时更新文本位置
   */
  move(deltaX, deltaY) {
    super.move(deltaX, deltaY)
    // 确保文本位置跟随节点移动
    const textPosition = this.getTextPosition()
    this.text.x = textPosition.x
    this.text.y = textPosition.y
  }
  
  /**
   * 设置节点位置时更新文本位置
   */
  moveTo(x, y) {
    super.moveTo(x, y)
    // 确保文本位置跟随节点位置
    const textPosition = this.getTextPosition()
    this.text.x = textPosition.x
    this.text.y = textPosition.y
  }
  
  /**
   * 更新文本时同步更新properties
   */
  updateText(value) {
    super.updateText(value)
    this.properties.functionName = value
    // 确保文本位置保持正确
    const textPosition = this.getTextPosition()
    this.text.x = textPosition.x
    this.text.y = textPosition.y
  }
  
  /**
   * 重写setProperties方法，确保属性更新时文本位置正确
   */
  setProperties(properties) {
    super.setProperties(properties)
    
    // 更新尺寸
    if (properties.width !== undefined) {
      this.width = properties.width
    }
    if (properties.height !== undefined) {
      this.height = properties.height
    }
    
    // 更新文本内容和位置
    if (properties.functionName !== undefined) {
      this.text.value = properties.functionName
      const textPosition = this.getTextPosition()
      this.text.x = textPosition.x
      this.text.y = textPosition.y
    }
  }
}

/**
 * 路由节点视图类 - 支持多种几何形状渲染
 */
class RouteNodeView extends RectNode {
  /**
   * 获取节点形状 - 根据shapeType渲染不同的几何形状
   * 图标+文字整体垂直居中布局
   */
  getShape() {
    const { x, y, width, height, properties } = this.props.model
    const style = this.props.model.getNodeStyle()
    const shapeType = properties.shapeType || 'rectangle'
    
    // 获取图标位置 - 根据形状类型调整图标大小和位置
    const iconPosition = this.props.model.getIconPosition()
    const iconSize = properties.shapeType === 'card' ? 16 : 28 // card类型使用更小的图标
    const iconX = iconPosition.x - iconSize / 2
    const iconY = iconPosition.y - iconSize / 2
    
    // 根据形状类型渲染不同的几何形状
    let shapeElement
    
    switch (shapeType) {
      case 'circle':
        // 圆形 - 使用较小的半径确保是圆形
        const radius = Math.min(width, height) / 2
        shapeElement = h('circle', {
          ...style,
          cx: x,
          cy: y,
          r: radius
        })
        break
        
      case 'ellipse':
        // 椭圆 - 使用不同的x轴和y轴半径
        shapeElement = h('ellipse', {
          ...style,
          cx: x,
          cy: y,
          rx: width / 2,
          ry: height / 2
        })
        break
        
      case 'diamond':
        // 菱形 - 使用polygon绘制
        const halfW = width / 2
        const halfH = height / 2
        const points = [
          `${x},${y - halfH}`,     // 上顶点
          `${x + halfW},${y}`,     // 右顶点
          `${x},${y + halfH}`,     // 下顶点
          `${x - halfW},${y}`      // 左顶点
        ].join(' ')
        
        shapeElement = h('polygon', {
          ...style,
          points: points
        })
        break
        
      case 'square':
      case 'rectangle':
        // 正方形/长方形 - 使用rect
        shapeElement = h('rect', {
          ...style,
          x: x - width / 2,
          y: y - height / 2,
          width,
          height
        })
        break
        
      case 'card':
        // 复合形状：整体背景色长条形 + 左侧图标 + 右侧白色文字区域（右上下边距一致）
        const margin = 3 // 整体边距
        const iconAreaSize = height - margin * 2 // 图标区域大小
        const textMargin = 2 // 文字区域的右上下边距（一致）
        const textAreaX = x - width / 2 + iconAreaSize + margin // 文字区域起始x（直接贴着图标区域）
        const textAreaWidth = width - (iconAreaSize + margin) - textMargin // 文字区域宽度（减去图标区域占用的总宽度和右边距）
        const textAreaHeight = height - textMargin * 2 // 文字区域高度（只考虑上下边距，不包含margin）
        
        // 阴影层 - 整体阴影
        const shadowLayer = h('rect', {
          x: x - width / 2 + 2,
          y: y - height / 2 + 2,
          width: width,
          height: height,
          rx: 8,
          ry: 8,
          fill: 'rgba(0, 0, 0, 0.08)', // 淡阴影
          stroke: 'none'
        })
        
        // 整体背景层 - 彩色背景（紫色）
        const backgroundLayer = h('rect', {
          x: x - width / 2,
          y: y - height / 2,
          width: width,
          height: height,
          rx: 8,
          ry: 8,
          fill: properties.backgroundColor || '#8b5cf6', // 整体使用背景色
          stroke: 'none'
        })
        
        // 右边文字区域的白色背景 - 选择性圆角：左侧无圆角，右侧与外层一致
        const textAreaY = y - height / 2 + textMargin
        const cornerRadius = 8 // 与外层背景圆角保持一致
        
        // 使用path绘制选择性圆角矩形：左上左下无圆角，右上右下有圆角
        const textAreaPath = [
          `M ${textAreaX} ${textAreaY}`, // 移动到左上角（无圆角）
          `L ${textAreaX + textAreaWidth - cornerRadius} ${textAreaY}`, // 直线到右上角圆角开始点
          `Q ${textAreaX + textAreaWidth} ${textAreaY} ${textAreaX + textAreaWidth} ${textAreaY + cornerRadius}`, // 右上角圆角
          `L ${textAreaX + textAreaWidth} ${textAreaY + textAreaHeight - cornerRadius}`, // 直线到右下角圆角开始点
          `Q ${textAreaX + textAreaWidth} ${textAreaY + textAreaHeight} ${textAreaX + textAreaWidth - cornerRadius} ${textAreaY + textAreaHeight}`, // 右下角圆角
          `L ${textAreaX} ${textAreaY + textAreaHeight}`, // 直线到左下角（无圆角）
          `Z` // 闭合路径
        ].join(' ')
        
        const textAreaBg = h('path', {
          d: textAreaPath,
          fill: '#ffffff', // 文字区域白色背景
          stroke: 'none'
        })
        
        shapeElement = h('g', {}, [shadowLayer, backgroundLayer, textAreaBg])
        break
        
      default:
        // 默认使用长方形
        shapeElement = h('rect', {
          ...style,
          x: x - width / 2,
          y: y - height / 2,
          width,
          height
        })
        break
    }
    
    // 图标元素 - 根据是否有图标决定显示内容
    let iconElement
    
    if (properties.iconSvg) {
      // 参考mb-icon.vue的实现，使用SVG+use方案，支持fill颜色设置
      const iconColor = properties.iconColor || '#ffffff'
      
      // 检查是否是symbol引用格式（#开头）
      const isSymbolRef = properties.iconSvg.startsWith('#')
      
      if (isSymbolRef) {
        // 使用SVG sprite方案，类似mb-icon.vue
        iconElement = h('svg', {
          x: iconX,
          y: iconY,
          width: iconSize,
          height: iconSize,
          fill: iconColor,  // 直接设置fill属性，这是正确的方法！
          viewBox: '0 0 24 24'
        }, [
          h('use', {
            'xlink:href': properties.iconSvg  // 例如: #mb-icon-home
          })
        ])
      } else {
        // 如果是URL格式，转换为对应的symbol引用
        // 从URL中提取图标名称，构建symbol引用
        const iconName = properties.iconSvg.split('/').pop().replace('.svg', '')
        const symbolId = `#mb-icon-${iconName}`
        
        iconElement = h('svg', {
          x: iconX,
          y: iconY,
          width: iconSize,
          height: iconSize,
          fill: iconColor,  // 直接设置fill属性
          viewBox: '0 0 24 24'
        }, [
          h('use', {
            'xlink:href': symbolId
          })
        ])
      }
    } else if (properties.shapeType === 'card') {
      // card类型没有图标时显示彩色文字图标
      iconElement = h('text', {
        x: iconPosition.x,
        y: iconPosition.y,
        textAnchor: 'middle',
        dominantBaseline: 'middle',
        fontSize: iconSize * 0.7, // 图标文字大小为图标尺寸的70%
        fill: properties.iconColor || '#ffffff',
        fontWeight: 'bold'
      }, '📱')
    } else {
      // 其他类型没有图标时显示默认圆圈
      iconElement = h('circle', {
        cx: iconPosition.x,
        cy: iconPosition.y,
        r: iconSize / 2,
        fill: '#f0f0f0',
        stroke: '#d9d9d9',
        strokeWidth: 1
      })
    }
    
    // 透明覆盖层 - 确保整个节点区域都可以响应拖拽事件
    let overlayElement
    
    if (shapeType === 'circle') {
      const radius = Math.min(width, height) / 2
      overlayElement = h('circle', {
        cx: x,
        cy: y,
        r: radius,
        fill: 'transparent',
        stroke: 'none',
        cursor: 'move'
      })
    } else if (shapeType === 'ellipse') {
      overlayElement = h('ellipse', {
        cx: x,
        cy: y,
        rx: width / 2,
        ry: height / 2,
        fill: 'transparent',
        stroke: 'none',
        cursor: 'move'
      })
    } else if (shapeType === 'diamond') {
      const halfW = width / 2
      const halfH = height / 2
      const points = [
        `${x},${y - halfH}`,
        `${x + halfW},${y}`,
        `${x},${y + halfH}`,
        `${x - halfW},${y}`
      ].join(' ')
      
      overlayElement = h('polygon', {
        points: points,
        fill: 'transparent',
        stroke: 'none',
        cursor: 'move'
      })
    } else {
      // 默认的矩形覆盖层（适用于square、rectangle、card等）
      overlayElement = h('rect', {
        x: x - width / 2,
        y: y - height / 2,
        width,
        height,
        fill: 'transparent',
        stroke: 'none',
        cursor: 'move'
      })
    }

    return h('g', {}, [
      shapeElement,    // 几何形状
      iconElement,     // 图标元素（包含mask和着色）
      overlayElement   // 透明覆盖层，置于最顶层
    ])
  }
}

export default {
  type: 'route-node',
  view: RouteNodeView,
  model: RouteNodeModel
} 