
function appComponent(app, item){
    item.compileJs = `(function(){
        ${item.compileJs}
    })()`
    let componentStyle = document.createElement("style");
    componentStyle.innerHTML = item.compileCss
    document.head.appendChild(componentStyle);
    app.component(item.name, eval(item.compileJs))
}

export async function loadDynamicComponent(app) {
    await $common.post('/system/component/list', { source: 0 }).then((res) => {
        res.data.forEach(it => {
            appComponent(app, it)
        })
    })
}
