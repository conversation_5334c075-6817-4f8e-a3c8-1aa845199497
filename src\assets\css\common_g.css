@font-face {
    font-family: PoetsenOne;
    src: url(../fonts/PoetsenOne.woff2) format('woff2');
    font-weight: 100;
    font-style: normal;
}

::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    overflow: auto
}

::-webkit-scrollbar-thumb {
    background-color: #e6e6e6;
    min-height: 25px;
    min-width: 25px;
    border: 1px solid #e0e0e0;
    border-radius: 99px
}

::-webkit-scrollbar-track {
    background-color: #f7f7f7;
    border: 1px solid #efefef
}

body {
    --mb-main-color: #409EFF;
    --mb-sidebar-width: 240px;
    --mb-main-icon-color: #909399;
    --mb-header-height: 50px;
    --mb-avatar-text-size: 18px;
    /*--mb-menu-item-height: 50px;*/
}

.el-header {
    --el-header-height: var(--mb-header-height);
}

.el-avatar {
    --el-avatar-text-size: var(--mb-avatar-text-size);
    --el-avatar-bg-color: var(--mb-main-color);
    border: 1px solid white;
}

.el-tabs {
    --el-tabs-header-height: 29px;
    font-size: 12px;
    padding-top: 3px;
    box-sizing: border-box;
}

.el-tabs__item {
    font-size: 12px;
}

.el-tabs--card>.el-tabs__header .el-tabs__nav.is-top>.el-tabs__item:first-child {
    margin-left: 6px;
}

.el-tabs--card>.el-tabs__header .el-tabs__nav.is-top>.el-tabs__item {
    border: none;
    border-radius: 2px 2px 0 0;
    margin: 0 2px;
    background-color: #f6f6f6;
    border-right: 1px solid #d9d9d9;
    box-shadow: 1px 0 3px rgb(174 174 174 / 35%);
}

.el-tabs--card>.el-tabs__header .el-tabs__nav.is-top>.el-tabs__item.is-active {
    border-bottom-color: transparent;
    background-color: #fff;
    box-shadow: 1px 0 3px rgb(174 174 174 / 35%), inset 0 2px 0 #0869bd;
}

.el-tabs__item.is-active {
    color: black;
}

.el-tabs--card>.el-tabs__header .el-tabs__nav {
    border: none;
}

.el-dialog__body {
    padding: 5px 20px;
}

.el-table thead.is-group th.el-table__cell {
    background: #e6e6e6;
}

.el-table th.el-table__cell {
    --el-table-header-bg-color: #e6e6e6;
    border-top: 1px #e6e6e6 solid;
    border-left: 1px transparent solid;
    border-right: 1px #f2f2f2 solid;
    border-bottom: 1px #e6e6e6 solid;
    padding: 0px;
}

.el-table th.el-table__cell .cell {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
    position: relative;
}

.el-table thead {
    --el-table-header-text-color: #000;
}

/*.el-sub-menu__title, .el-menu-item{*/
/*  height: var(--mb-menu-item-height) !important;*/
/*}*/
.app-container hr {
    border: none;
    height: 1px;
    background: #F3F3F3;
}

.toolbar-container {
    margin-bottom: 10px
}

.toolbar-container>div,
.toolbar-container>button {
    margin-left: 6px;
}

.toolbar-container>div:nth-child(1),
.toolbar-container>button:nth-child(1) {
    margin-left: 0px;
}

.el-table__row {
    height: 29px;
}

.el-table .el-table__cell {
    padding: 0px 0;
    min-width: 0;
    box-sizing: border-box;
    text-overflow: ellipsis;
    vertical-align: middle;
    /* position: relative; */
    text-align: left;
    z-index: 1;
}

.clear {
    clear: both;
}

a {
    text-decoration: none;
}

.el-form--inline .el-form-item {
    display: inline-flex;
    vertical-align: middle;
    margin-right: 8px;
}

.el-form-item__label {
    display: inline-flex;
    justify-content: flex-end;
    align-items: flex-start;
    flex: 0 0 auto;
    font-size: var(--el-form-label-font-size);
    color: #000;
    height: 24px;
    line-height: 24px;
    padding: 0 6px 0 0;
    box-sizing: border-box;
    font-size: 12px;
}


.el-input {
    --el-input-height: var(--el-component-size);
    position: relative;
    font-size: var(--el-font-size-base);
    display: inline-flex;
    width: 100%;
    line-height: var(--el-input-height);
    box-sizing: border-box;
    height: 24px;
}

.el-input--prefix .el-input__inner {
    padding-left: 0px !important;
}

.el-form-item__content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    flex: 1;
    line-height: 22px;
    font-size: var(--font-size);
    min-width: 0;
}

/* 
.el-select .el-select__tags .el-tag--info {
  background-color: var(--el-fill-color);
  width: auto;
  height: 20px;
} */

/* 修改el-select下拉选框样式 */
.el-select__tags-text {
    max-width: 100px !important;
}

.el-select .el-select__tags>span {
    display: none !important;
}

.el-select .el-select__tags .el-tag--info {
    background-color: #fff !important;
}

.el-tag .el-tag__close {
    display: none !important;
}

.el-select__tags {
    display: none;
}

.el-select-tags-wrapper {
    display: none !important;
}


.el-dropdown {
    line-height: 26px
}

.el-button {
    /* width: 70px; */
    height: 24px;
    font-size: 12px;
}

.el-button.is-link:not(.is-disabled):focus,
.el-button.is-link:not(.is-disabled):hover {
    color: #0869bd;
}

.el-button+.el-button {
    margin-left: 0;
}

.dialog-footer .el-button+.el-button {
    margin-left: 12px;
}

.el-pagination .el-select .el-input {
    width: auto;
}

.el-select .el-input .el-select__caret {
    color: #000;
    font-size: 12px;
}
.el-select .el-input .el-select__caret.el-icon{
    z-index: 1;
}
.picker_span {
    font-size: 12px;
    display: block;
    margin: 0 5px;
}

.el-pager li {
    font-size: 12px !important;
    font-weight: normal !important;
}

.btn-prev,
.btn-next {
    width: 24px;
    height: 24px;
}

.el-select .el-select-tags-wrapper.has-prefix {
    margin-left: 6px;
}

.el-pagination.is-background .btn-prev,
.btn-next,
.el-pagination.is-background .btn-next,
.el-pager li {
    background-color: var(--el-pagination-button-bg-color);
    color: var(--el-text-color-regular);
    min-width: 24px;
    border-radius: 2px;
    width: 24px;
    height: 24px;
}

.el-main {
    --el-main-padding: 0px;
}

.el-table {
    --el-table-border-color: #fff;
    font-size: 12px;
    --el-table-current-row-bg-color: #bcd5e6 !important;
    --el-table-tr-bg-color: none;
}

/* .el-table .el-table__body{
  padding-bottom: 20px;
} */

.el-table__row {
    height: 22px;
    line-height: 22px;
}

.el-select {
    display: inline-block;
    position: relative;
    line-height: 22px;
}

.cell>.el-dropdown>.el-dropdown-link>.el-icon {
    --color: inherit;
    height: 1em;
    width: 1em;
    line-height: 1.2em;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 7px;
    fill: currentColor;
    color: var(--color);
    font-size: inherit;
}

.el-pagination .el-pagination__sizes .el-select>.select-trigger>.el-input {
    width: 60px;
}

.el-pagination>.el-pagination__jump>.el-input .el-input__wrapper .el-input__inner {
    padding: 0;
    width: 30px;
}

.el-pagination.is-background .btn-next:disabled,
.el-pagination.is-background .btn-prev:disabled {
    color: var(--el-text-color-placeholder);
    background-color: #fff;
}

.el-pagination.is-background .el-pager li:not(.is-disabled).is-active {
    background-color: #fff;
    color: #000;
    font-weight: 700;
    padding: 0;
}

.el-date-editor.el-input {
    height: 24px !important;
}

.el-date-editor.el-input__wrapper {
    box-shadow: 0 0 0 1px #aeaeae inset;
}

.el-date-editor.el-input__wrapper:hover {
    box-shadow: 0 0 0 1px #1c82cb inset;
}

.el-date-editor .el-range-input {
    font-size: 12px !important;
}


.el-form .el-form-item .el-form-item__label .el-form-item__content {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    flex: 1;
    height: 25px;
    line-height: 26px;
    font-size: var(--font-size);
    min-width: 0;
}

.el-input>.el-input__wrapper {
    padding: 1px 0 1px 2px;
    font-size: 12px;
}

.el-range-editor.el-input__wrapper {
    display: inline-flex;
    align-items: center;
    padding: 0 5px !important;
    height: 25px;
    width: 338px;
}

.el-table__body-wrapper .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view .el-table__body .el-table__row .el-table__cell {
    height: 30px;
}

.el-tabs--border-card>.el-tabs__header {
    background-color: #ffffff;
    border-bottom: none;
    margin: 0;
}

.el-tabs--border-card>.el-tabs__header .el-tabs__item {
    transition: all var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier);
    margin-top: -1px;
    color: #000;
    border: none !important;
}

.el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
    color: #000;
    border: none;
    background: #BCD5E6;
}

.el-form>span>.el-form-item {
    display: inline-flex;
    vertical-align: middle;
    /* width: 150px; */
    /* margin-right: 8px; */
}

/* .el-form > span > .el-form-item:last-child {
  display: inline-flex;
  vertical-align: middle;
  width: 340px;
  margin-right: 8px;
} */
form>span:nth-child(10)>.el-form-item {
    display: inline-flex;
    vertical-align: middle;
    width: 340px;
    margin-right: 8px;
}

.el-input__wrapper {
    color: #000;
}

.el-table__body>tbody>.current-row {
    background-color: #bcd5e6 !important;
}

.el-table__body>tbody>.current-row>.current-cell {
    background-color: #c1ddf1 !important;
}

.el-form-item__content .el-input .el-input__wrapper {
    box-shadow: 0 0 0 1px #aeaeae inset;
}

.el-form-item__content .el-input .el-input__wrapper:hover {
    box-shadow: 0 0 0 1px #1c82cb inset;
}

.search {
    padding: 8px 6px 4px 6px;
    box-sizing: border-box;
}

.search .el-form-item,
.filter-container .el-form-item {
    --font-size: 14px;
    margin-bottom: 9px;
}

.search input {
    width: 100px;
    padding-left: 5px;
}



.el-table .cell {
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: clip;
    white-space: nowrap;
    word-break: break-all;
    line-height: 23px;
    flex-wrap: nowrap;
    padding: 0 12px;
}

.el-tabs--border-card>.el-tabs__content {
    padding: 0;
}

.el-textarea__inner {
    box-shadow: 0 0 0 1px #979899 inset;
    padding: 5px !important;
}

.el-upload--picture-card {
    --el-upload-picture-card-size: 60px;
    background-color: #dbdbdb;
}

.el-dropdown {
    line-height: 26px;
    font-size: 12px;
    font-weight: normal;
    color: #000;
}

.el-input-number {
    position: relative;
    display: inline-block;
    width: 100px;
    line-height: 8px;
}

.el-input-number__decrease,
.el-input-number__increase {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 22px;
    position: absolute;
    z-index: 1;
    /* top: 4px; */
    bottom: 1px;
    width: 29px;
    background: var(--el-fill-color-light);
    color: var(--el-text-color-regular);
    cursor: pointer;
    font-size: 13px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.el-input__prefix-inner>:first-child,
.el-input__prefix-inner>:first-child.el-input__icon {
    margin-left: 8px !important;
}

.el-table__row .current-row {
    background-color: #bcd5e6 !important;
}

.el-dialog__footer {
    text-align: center;
}

.el-tab-pane>.el-tabs>.el-tabs__header>.el-tabs__nav-wrap>.el-tabs__nav-scroll {
    border-bottom: 0;
    margin-bottom: 0;
}

.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
    /* background-color: var(--el-table-current-row-bg-color) !important; */
    /* background-color: #ffffff !important; */
}

.el-table--enable-row-hover .el-table__body .current-row:hover>td.el-table__cell {
    /* background-color: var(--el-table-current-row-bg-color) !important; */
    background-color: #bcd5e6 !important;
}

.el-tabs__content .is-guttered>.el-form-item {
    width: 210px;
}

.el-form-item.is-error .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}

.gift-uploadFile>.el-upload>.el-button {
    width: 110px !important;
}

.invoice-list>.el-form>.el-row input {
    width: 120px !important;
}

.save-submit-next>.el-form-item__content {
    margin-left: 0 !important;
}

.el-select .el-input__inner {
    padding-left: 5px;
}

.el-input__inner {
    padding-left: 5px;
}

.el-input__suffix-inner>:first-child {
    margin-right: 5px !important;
}

.el-cascader {
    line-height: 24px !important;
}

.el-radio {
    height: 24px !important;
}

.el-checkbox {
    height: 24px !important;
}

/* 修改按钮样式、图标 */
.actbtns {
    margin-right: 8px;
    padding: 0 7px 0 9px !important;
    border: 1px solid #999;
    border-radius: 3px;
    height: 24px !important;
    color: #000;
    line-height: 22px;
    width: auto;
}

.actbtns i {
    font-size: 14px;
    margin-right: 5px;
}

.i-green {
    color: #558364 !important;
}

.i-blue {
    color: #4b6fa7 !important;
}

.i-blue-sky {
    color: #1bc6ff !important;  
}

.i-yellow {
    color: #d7af38 !important;
}

.i-orange {
    color: orange !important;
}

.i-red {
    color: #d35e4d !important;
}

.i-black {
    color: #333;
}

.actbtns:hover {
    background: none !important;
    border-color: #1c82cb !important;
    color: #1c82cb !important;
}

.actbtns:focus {
    background: none !important;
    border-color: #999 !important;
    color: #000 !important;
}

.el-radio {
    height: 24px !important;
}

.el-checkbox {
    height: 24px !important;
}

/* 修改按钮样式、图标 */
.actbtns {
    margin-right: 8px;
    padding: 0 7px 0 9px !important;
    border: 1px solid #999;
    border-radius: 3px;
    height: 24px !important;
    color: #000;
    line-height: 22px;
    width: auto;
}

.actbtns i {
    font-size: 14px;
    margin-right: 5px;
}

.i-green {
    color: #558364 !important;
}

.i-blue {
    color: #4b6fa7 !important;
}

.i-yellow {
    color: #d7af38 !important;
}

.i-red {
    color: #d35e4d !important;
}

.actbtns:hover {
    background: none !important;
    border-color: #1c82cb !important;
    color: #1c82cb !important;
}

.actbtns:focus {
    background: none !important;
    border-color: #999 !important;
    color: #000 !important;
}

.el-radio {
    height: 24px !important;
}

.el-checkbox {
    height: 24px !important;
}

/* 修改按钮样式、图标 */
.actbtns {
    margin-right: 8px;
    padding: 0 7px 0 9px !important;
    border: 1px solid #999;
    border-radius: 3px;
    height: 24px !important;
    color: #000;
    line-height: 22px;
    width: auto;
}

.actbtns i {
    font-size: 14px;
    margin-right: 5px;
}

.i-green {
    color: #558364 !important;
}

.i-blue {
    color: #4b6fa7 !important;
}

.i-yellow {
    color: #d7af38 !important;
}

.i-red {
    color: #d35e4d !important;
}

.i-blues {
    color: #02b5f7 !important;
}

.actbtns:hover {
    background: none !important;
    border-color: #1c82cb !important;
    color: #1c82cb !important;
}

.actbtns:focus {
    background: none !important;
    border-color: #999 !important;
    color: #000 !important;
}

.el-timeline {
    max-height: 400px;
    overflow: auto;
}

.el-timeline-item__content {
    max-height: 100px;
    overflow: auto;
}

.el-table .el-popper .el-popper__arrow::before {
    background: #303133;
}


.el-table {
    color: #000;
    -moz-user-select: auto;
    -webkit-user-select: auto;
    -ms-user-select: auto;
}

.el-table thead th {
    font-weight: normal;
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background-color: #f6f6f6;
}
.el-table__body>tbody>.current-row:hover>td.el-table__cell {
    background-color: #bcd5e6 !important;
}
.el-table--striped .el-table__body tr.el-table__row--striped:hover>td.el-table__cell {
    background-color: #f6f6f6;
}

.el-table--striped .el-table__body .el-table__row:hover>td.el-table__cell {
    background-color: #fff;
}

.el-table--striped .el-table__body .el-table__row:hover>td.current-cell {
    background-color: #c1ddf1 !important;
}

.el-table__expand-icon {
    color: #000;
}

.el-scrollbar__wrap {
    padding-bottom: 20px;
    box-sizing: border-box;
}
.el-scrollbar__thumb{
    background: #000 !important;
    border-radius: 0px !important;
}
.table:hover .el-scrollbar__bar.is-horizontal {
    height: 14px !important;
}