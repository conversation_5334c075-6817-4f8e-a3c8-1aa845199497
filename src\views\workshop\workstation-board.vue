<template>
  <div class="workstation-board">
    <div class="status-bar"></div>
    <div class="header">
      <div class="logo">
        <span class="logo-text">志友</span>
        <span>工位看板</span>
      </div>
      <div class="user-info">
        <span>{{ userData.name }}</span>
        <span>{{ userData.position }}</span>
        <span>退出</span>
      </div>
    </div>
    
    <div class="main-container">
      <!-- 右侧功能按钮 -->
      <div class="side-buttons">
        <div class="btn-item">
          <div class="icon">📚</div>
          <div class="text">SOP</div>
        </div>
        <div class="btn-item">
          <div class="icon">📝</div>
          <div class="text">技术</div>
        </div>
        <div class="btn-item">
          <div class="icon">📊</div>
          <div class="text">考勤</div>
        </div>
        <div class="btn-item">
          <div class="icon">📈</div>
          <div class="text">绩效</div>
        </div>
        <div class="btn-item">
          <div class="icon">🔍</div>
          <div class="text">质检</div>
        </div>
        <div class="btn-item">
          <div class="icon">➕</div>
          <div class="text">更多</div>
        </div>
      </div>
      
      <div class="content-wrapper">
        <div class="content">
          <!-- 左侧个人信息 -->
          <div class="left-panel">
            <div class="user-card">
              <img :src="userData.avatar || 'https://iph.href.lu/150x200'" alt="用户头像" class="avatar" />
              <div class="info-list">
                <div class="info-item">
                  <span class="label">姓名</span>
                  <span class="value">{{ userData.name }}</span>
                </div>
                <div class="info-item">
                  <span class="label">线别</span>
                  <span class="value">{{ userData.line }}</span>
                </div>
                <div class="info-item">
                  <span class="label">工位</span>
                  <span class="value">{{ userData.position }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 中间图表区域 -->
          <div class="middle-panel">
            <div class="chart-title">每小时生产数量(总计:{{ totalProduction }}台)</div>
            <div class="chart-container">
              <div id="hourly-chart" ref="hourlyChartRef"></div>
            </div>
          </div>
          
          <!-- 右侧饼图区域 -->
          <div class="right-panel">
            <div class="chart-title">异常分布图</div>
            <div class="chart-container">
              <div id="pie-chart" ref="pieChartRef"></div>
            </div>
          </div>
        </div>
        
        <!-- 底部表格区域 -->
        <div class="table-section">
          <div class="tab-header">
            <div class="tab-item active">生产记录</div>
            <div class="tab-item">技术记录</div>
            <div class="tab-item">考勤记录</div>
            <div class="tab-item">绩效记录</div>
            <div class="tab-item">质检记录</div>
          </div>
          
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>订单号</th>
                  <th>产品名称</th>
                  <th>开工时间</th>
                  <th>订单数</th>
                  <th>完成数</th>
                  <th>完成率</th>
                  <th>合格数</th>
                  <th>合格率</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in productionRecords" :key="index">
                  <td>{{ item.orderId }}</td>
                  <td>{{ item.productName }}</td>
                  <td>{{ item.startTime }}</td>
                  <td>{{ item.orderQuantity }}</td>
                  <td>{{ item.completedQuantity }}</td>
                  <td>{{ item.completionRate }}%</td>
                  <td>{{ item.qualifiedQuantity }}</td>
                  <td>{{ item.qualificationRate }}%</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import * as echarts from 'echarts'

// 用户数据
const userData = reactive({
  name: '王建立',
  avatar: '',
  line: '智能总装线',
  position: '除水'
})

// 生产记录数据
const productionRecords = ref([
  {
    orderId: 'XSDD17100005',
    productName: '拓浦智能电饭煲3L',
    startTime: '2017/10/10 17:46:08',
    orderQuantity: 20,
    completedQuantity: 20,
    completionRate: 100.00,
    qualifiedQuantity: 20,
    qualificationRate: 100.00
  },
  {
    orderId: 'XSDD17100004',
    productName: '拓浦智能电饭煲3L',
    startTime: '2017/10/11 13:46:09',
    orderQuantity: 50,
    completedQuantity: 50,
    completionRate: 100.00,
    qualifiedQuantity: 50,
    qualificationRate: 100.00
  },
  {
    orderId: 'XSDD17100003',
    productName: '拓浦智能电饭煲5L',
    startTime: '2017/10/12 09:30:15',
    orderQuantity: 30,
    completedQuantity: 28,
    completionRate: 93.33,
    qualifiedQuantity: 26,
    qualificationRate: 92.86
  },
  {
    orderId: 'XSDD17100002',
    productName: '拓浦智能电压力锅4L',
    startTime: '2017/10/13 08:15:22',
    orderQuantity: 45,
    completedQuantity: 45,
    completionRate: 100.00,
    qualifiedQuantity: 43,
    qualificationRate: 95.56
  },
  {
    orderId: 'XSDD17100001',
    productName: '拓浦智能电蒸锅',
    startTime: '2017/10/14 10:22:35',
    orderQuantity: 25,
    completedQuantity: 25,
    completionRate: 100.00,
    qualifiedQuantity: 25,
    qualificationRate: 100.00
  },
  {
    orderId: 'XSDD17090010',
    productName: '拓浦智能电饭煲3L',
    startTime: '2017/09/30 14:10:08',
    orderQuantity: 60,
    completedQuantity: 60,
    completionRate: 100.00,
    qualifiedQuantity: 58,
    qualificationRate: 96.67
  },
  {
    orderId: 'XSDD17090009',
    productName: '拓浦智能电饭煲5L',
    startTime: '2017/09/29 11:25:18',
    orderQuantity: 40,
    completedQuantity: 40,
    completionRate: 100.00,
    qualifiedQuantity: 40,
    qualificationRate: 100.00
  }
])

// 图表引用
const hourlyChartRef = ref(null)
const pieChartRef = ref(null)
const totalProduction = ref(270)

// 初始化每小时生产量图表
const initHourlyChart = () => {
  const hourlyChart = echarts.init(hourlyChartRef.value)
  
  const option = {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'],
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '生产数量',
        type: 'bar',
        barWidth: '60%',
        data: [25, 32, 18, 34, 50, 25, 45, 32, 18, 15],
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  }
  
  hourlyChart.setOption(option)
}

// 初始化异常分布饼图
const initPieChart = () => {
  const pieChart = echarts.init(pieChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '13%',
      top: '20%',
      itemWidth: 14,
      itemHeight: 14,
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '异常分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['33%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 35, name: '设备故障', itemStyle: { color: '#F56C6C' } },
          { value: 20, name: '物料缺失', itemStyle: { color: '#E6A23C' } },
          { value: 15, name: '人员缺勤', itemStyle: { color: '#909399' } },
          { value: 10, name: '其他原因', itemStyle: { color: '#409EFF' } },
          { value: 45, name: '正常生产', itemStyle: { color: '#67C23A' } }
        ]
      }
    ]
  }
  
  pieChart.setOption(option)
}

// 页面加载完成后初始化图表
onMounted(() => {
  initHourlyChart()
  initPieChart()
  
  // 窗口大小变化时重新渲染图表
  window.addEventListener('resize', () => {
    if (hourlyChartRef.value) {
      echarts.getInstanceByDom(hourlyChartRef.value)?.resize()
    }
    if (pieChartRef.value) {
      echarts.getInstanceByDom(pieChartRef.value)?.resize()
    }
  })
})
</script>

<style scoped>
.workstation-board {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f2f5;
  color: #333;
  font-family: Arial, sans-serif;
}

.status-bar {
  height: 20px;
  background-color: #2c5cff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: #2c5cff;
  color: white;
  z-index: 10;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 26px;
  font-weight: bold;
  margin-right: 15px;
  color: #ffffff;
  font-family: "Microsoft YaHei", sans-serif;
}

.logo span:last-child {
  font-size: 20px;
  font-weight: bold;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info span {
  margin-left: 15px;
  font-size: 16px;
}

.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.side-buttons {
  width: 80px;
  background-color: #2c5cff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  gap: 20px;
}

.btn-item {
  width: 60px;
  height: 60px;
  background-color: #4c6fff;
  color: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-item:hover {
  transform: scale(1.05);
  background-color: #5d7fff;
}

.icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.text {
  font-size: 12px;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content {
  display: flex;
  padding: 20px;
  gap: 20px;
  flex: 0 0 auto;
  height: 300px;
}

.left-panel {
  flex: 0 0 250px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
}

.user-card {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 100px;
  height: 130px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.info-list {
  width: 100%;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.label {
  width: 50px;
  color: #909399;
}

.value {
  flex: 1;
  font-weight: bold;
}

.middle-panel, .right-panel {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}

.chart-container {
  flex: 1;
  position: relative;
}

#hourly-chart, #pie-chart {
  width: 100%;
  height: 100%;
}

.table-section {
  flex: 1;
  margin: 0 20px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tab-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.tab-item {
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.tab-item.active {
  color: #2c5cff;
  border-bottom: 2px solid #2c5cff;
  background-color: white;
}

.table-container {
  flex: 1;
  padding: 15px;
  overflow: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px 8px;
  text-align: center;
  border-bottom: 1px solid #ebeef5;
}

th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

tr:hover {
  background-color: #f5f7fa;
}
</style> 