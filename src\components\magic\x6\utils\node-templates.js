// 节点模板定义
import { Graph } from '@antv/x6'

// 基础节点样式
const baseNodeStyle = {
  stroke: '#5F95FF',
  strokeWidth: 1,
  fill: '#ffffff',
}

// 定义基础节点模板
export const getBaseNodeTemplates = () => {
  return [
    {
      id: 'rect',
      shape: 'rect',
      width: 100,
      height: 50,
      label: '矩形节点',
      attrs: {
        body: {
          ...baseNodeStyle,
        },
        label: {
          fontSize: 12,
          fill: '#333333',
        },
      },
      ports: getDefaultPorts(),
    },
    {
      id: 'circle',
      shape: 'circle',
      width: 60,
      height: 60,
      label: '圆形节点',
      attrs: {
        body: {
          ...baseNodeStyle,
        },
        label: {
          fontSize: 12,
          fill: '#333333',
        },
      },
      ports: getDefaultPorts(),
    },
    {
      id: 'ellipse',
      shape: 'ellipse',
      width: 100,
      height: 50,
      label: '椭圆节点',
      attrs: {
        body: {
          ...baseNodeStyle,
        },
        label: {
          fontSize: 12,
          fill: '#333333',
        },
      },
      ports: getDefaultPorts(),
    },
    {
      id: 'polygon',
      shape: 'polygon',
      width: 80,
      height: 80,
      label: '多边形',
      attrs: {
        body: {
          ...baseNodeStyle,
          refPoints: '0,10 10,0 20,10 10,20',
        },
        label: {
          fontSize: 12,
          fill: '#333333',
        },
      },
      ports: getDefaultPorts(),
    },
    {
      id: 'diamond',
      shape: 'polygon',
      width: 90,
      height: 60,
      label: '菱形',
      attrs: {
        body: {
          ...baseNodeStyle,
          refPoints: '0,10 10,0 20,10 10,20',
        },
        label: {
          fontSize: 12,
          fill: '#333333',
        },
      },
      ports: getDefaultPorts(),
    },
  ]
}

// 定义路由节点模板
export const getRouteNodeTemplates = () => {
  return [
    {
      id: 'route-rect',
      shape: 'rect',
      width: 80,
      height: 80,
      label: '页面节点',
      attrs: {
        body: {
          fill: '#ffffff',
          stroke: '#e0e0e0',
          strokeWidth: 1,
          rx: 6,
          ry: 6,
        },
        label: {
          fontSize: 9,
          fill: '#666666',
          textWrap: {
            width: 70,
            height: 20,
            ellipsis: true,
          },
        },
      },
      data: {
        routeName: '',
        routePath: '',
      },
      ports: getDefaultPorts(),
    },
    {
      id: 'route-start',
      shape: 'circle',
      width: 80,
      height: 80,
      label: '开始',
      attrs: {
        body: {
          fill: '#52c41a',
          stroke: '#e0e0e0',
          strokeWidth: 1,
          r: 25,
        },
        label: {
          fontSize: 9,
          fill: '#ffffff',
        },
      },
      data: {
        routeName: '开始',
        routePath: '/',
      },
      ports: getDefaultPorts(),
    },
    {
      id: 'route-end',
      shape: 'circle',
      width: 80,
      height: 80,
      label: '结束',
      attrs: {
        body: {
          fill: '#ff4d4f',
          stroke: '#e0e0e0',
          strokeWidth: 1,
          r: 25,
        },
        label: {
          fontSize: 9,
          fill: '#ffffff',
        },
      },
      data: {
        routeName: '结束',
        routePath: '',
      },
      ports: getDefaultPorts(),
    },
  ]
}

// 获取默认连接桩
const getDefaultPorts = () => {
  return {
    groups: {
      top: {
        position: 'top',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
      bottom: {
        position: 'bottom',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
      left: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
    },
    items: [
      {
        group: 'top',
      },
      {
        group: 'right',
      },
      {
        group: 'bottom',
      },
      {
        group: 'left',
      },
    ],
  }
}

// 创建节点实例
export const createNode = (graph, nodeConfig) => {
  if (!graph || !nodeConfig) return null
  
  return graph.createNode(nodeConfig)
} 