# 订单管理模板测试指南

## 测试环境准备

### 1. 启动项目
```bash
npm run dev
# 或
yarn dev
```

### 2. 访问页面
导航到订单管理模板页面：`/template/order-table`

## 功能测试清单

### 1. 基础界面测试
- [ ] 页面正常加载，无控制台错误
- [ ] 搜索框正常显示
- [ ] 状态筛选标签页正常显示
- [ ] 工具栏按钮正常显示
- [ ] 主表格正常显示

### 2. 搜索和筛选功能测试
- [ ] 输入采购单号进行搜索
- [ ] 选择日期范围进行搜索
- [ ] 输入供应商名称进行搜索
- [ ] 点击不同状态标签页进行筛选
- [ ] 验证筛选结果的准确性

### 3. 表格操作测试
- [ ] 单击选择表格行
- [ ] 双击打开订单详情抽屉
- [ ] 验证选中状态的视觉反馈
- [ ] 测试表格滚动和列宽调整

### 4. 工具栏功能测试
- [ ] 未选择订单时，按钮应为禁用状态
- [ ] 选择订单后，按钮应为启用状态
- [ ] 点击"编辑订单"按钮打开详情抽屉
- [ ] 验证按钮的加载状态

### 5. 订单详情抽屉测试
- [ ] 抽屉正常打开和关闭
- [ ] 主单信息正确显示
- [ ] 明细表格正确显示
- [ ] 明细行选择功能正常
- [ ] 表单字段编辑功能正常

### 6. 编辑功能测试
- [ ] 选择明细行进行编辑
- [ ] 修改物料信息
- [ ] 修改数量信息
- [ ] 修改急料标识
- [ ] 验证数据实时同步

### 7. 打印功能测试
- [ ] 点击"打印主单"按钮
- [ ] 验证主单打印预览内容
- [ ] 点击"打印明细"按钮
- [ ] 验证明细打印预览内容
- [ ] 测试实际打印功能

### 8. 数据保存测试
- [ ] 修改订单信息后点击保存
- [ ] 验证保存成功提示
- [ ] 验证数据持久化
- [ ] 测试保存失败的错误处理

### 9. 响应式测试
- [ ] 在不同屏幕尺寸下测试界面适配
- [ ] 测试移动端兼容性
- [ ] 验证抽屉在小屏幕下的表现

### 10. 错误处理测试
- [ ] 网络错误时的处理
- [ ] 数据加载失败的处理
- [ ] 权限不足的处理
- [ ] 无效操作的提示

## 性能测试

### 1. 加载性能
- [ ] 页面首次加载时间
- [ ] 数据获取响应时间
- [ ] 大数据量下的表格渲染性能

### 2. 交互性能
- [ ] 筛选操作的响应速度
- [ ] 抽屉打开/关闭的流畅度
- [ ] 表格滚动的流畅度

## 兼容性测试

### 1. 浏览器兼容性
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Safari (最新版本)
- [ ] Edge (最新版本)

### 2. 设备兼容性
- [ ] 桌面端 (1920x1080)
- [ ] 笔记本 (1366x768)
- [ ] 平板 (768x1024)
- [ ] 手机 (375x667)

## 常见问题排查

### 1. 页面无法加载
- 检查控制台错误信息
- 验证路由配置
- 检查权限设置

### 2. 数据不显示
- 检查API接口是否正常
- 验证数据格式
- 检查网络连接

### 3. 打印功能异常
- 检查浏览器弹窗设置
- 验证打印权限
- 测试不同浏览器

### 4. 编辑功能异常
- 检查表单验证规则
- 验证数据绑定
- 检查保存接口

## 测试数据准备

### 1. 模拟数据结构
```javascript
// 主单数据示例
const masterData = {
  BillNum: "SO202401001",
  BillDate: "2024-01-15",
  pbillType: "采购订单",
  suppName: "测试供应商",
  mesStatus: "已审核",
  memo: "测试备注",
  maker: "测试用户",
  company: "测试公司",
  factroy: "测试工厂"
}

// 明细数据示例
const detailData = [
  {
    sguid: "detail001",
    itemno: "MAT001",
    itemName: "测试物料1",
    itemModel: "型号A",
    quan: 100,
    itemUnit: "个",
    urgentflag: "非急料",
    RemarkSub: "明细备注1"
  }
]
```

### 2. 测试场景数据
- 正常订单数据
- 空数据情况
- 大量数据情况
- 异常数据情况

## 测试报告模板

### 测试结果记录
- 测试时间：
- 测试环境：
- 测试人员：
- 通过项目：
- 失败项目：
- 问题描述：
- 修复建议：

### 性能指标记录
- 页面加载时间：
- 数据获取时间：
- 交互响应时间：
- 内存使用情况：

## 自动化测试建议

### 1. 单元测试
- 组件渲染测试
- 函数逻辑测试
- 数据处理测试

### 2. 集成测试
- API接口测试
- 组件交互测试
- 数据流测试

### 3. E2E测试
- 完整业务流程测试
- 用户操作路径测试
- 跨浏览器测试

## 注意事项

1. **数据安全**：测试时使用模拟数据，避免操作生产数据
2. **权限测试**：确保在不同权限级别下测试功能
3. **错误恢复**：测试系统在错误后的恢复能力
4. **用户体验**：关注界面友好性和操作便利性
5. **性能监控**：持续监控系统性能指标
