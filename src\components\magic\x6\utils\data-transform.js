// 数据转换工具

/**
 * 将图形数据转换为标准JSON格式
 * @param {Object} graphData - 图形数据对象（通常是graph.toJSON()的结果）
 * @param {Object} options - 转换选项
 * @returns {Object} 标准化的JSON数据
 */
export const transformGraphDataToJSON = (graphData, options = {}) => {
  if (!graphData) return null
  
  // 格式化节点数据
  const nodes = graphData.cells
    .filter(cell => cell.shape !== 'edge')
    .map(node => {
      return {
        id: node.id,
        type: node.shape,
        label: node.attrs?.label?.text || '',
        position: {
          x: node.position.x,
          y: node.position.y,
        },
        size: {
          width: node.size.width,
          height: node.size.height,
        },
        data: node.data || {},
        attrs: node.attrs || {},
        ports: node.ports || {},
      }
    })
  
  // 格式化边数据
  const edges = graphData.cells
    .filter(cell => cell.shape === 'edge')
    .map(edge => {
      return {
        id: edge.id,
        source: {
          cell: edge.source.cell,
          port: edge.source.port,
        },
        target: {
          cell: edge.target.cell,
          port: edge.target.port,
        },
        labels: edge.labels || [],
        data: edge.data || {},
        attrs: edge.attrs || {},
      }
    })
  
  return {
    nodes,
    edges,
    ...options,
  }
}

/**
 * 将标准JSON格式转换为图形数据
 * @param {Object} jsonData - 标准JSON数据
 * @returns {Object} 图形数据对象
 */
export const transformJSONToGraphData = (jsonData) => {
  if (!jsonData) return null
  
  const { nodes = [], edges = [] } = jsonData
  
  // 转换节点数据
  const cellNodes = nodes.map(node => {
    return {
      id: node.id,
      shape: node.type,
      attrs: node.attrs,
      position: node.position,
      size: node.size,
      data: node.data,
      ports: node.ports,
    }
  })
  
  // 转换边数据
  const cellEdges = edges.map(edge => {
    return {
      id: edge.id,
      shape: 'edge',
      source: edge.source,
      target: edge.target,
      labels: edge.labels,
      attrs: edge.attrs,
      data: edge.data,
    }
  })
  
  return {
    cells: [...cellNodes, ...cellEdges],
  }
}

/**
 * 将路由设计数据转换为路由配置
 * @param {Object} graphData - 图形数据对象
 * @returns {Array} 路由配置数组
 */
export const transformToRouteConfig = (graphData) => {
  if (!graphData) return []
  
  const { nodes = [] } = transformGraphDataToJSON(graphData)
  
  return nodes
    .filter(node => node.data && node.data.routeName && node.data.routePath)
    .map(node => {
      return {
        name: node.data.routeName,
        path: node.data.routePath,
        id: node.id,
        nodeType: node.type,
        label: node.label,
      }
    })
}

/**
 * 判断数据格式是否有效
 * @param {Object} data - 数据对象
 * @returns {Boolean} 是否有效
 */
export const isValidGraphData = (data) => {
  if (!data || typeof data !== 'object') return false
  
  // 检查是否有cells属性
  if (!data.cells || !Array.isArray(data.cells)) {
    // 检查是否有nodes和edges属性
    if ((!data.nodes || !Array.isArray(data.nodes)) || 
        (!data.edges || !Array.isArray(data.edges))) {
      return false
    }
  }
  
  return true
} 