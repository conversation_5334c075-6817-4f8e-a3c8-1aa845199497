<template>
  <div class="mb-logicflow-design">
    <!-- 顶部工具栏 -->
    <mb-logicflow-toolbar
      :logic-flow="logicFlow"
      @save="handleSave"
      @clear="handleClear"
    />

    <!-- 主要内容区域 -->
    <div class="design-content">
      <!-- 左侧拖拽面板 -->
      <div class="sidebar-left">
        <mb-logicflow-sidebar @drag-in-node="handleDragInNode" />
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <div ref="canvasRef" class="logic-flow-canvas"></div>
      </div>

      <!-- 右侧属性面板 -->
              <div v-if="showPropertyPanel && (selectedNodes.length > 0 || selectedEdges.length > 0)" class="sidebar-right">
          <mb-logicflow-property
            :elementsStyle="properties"
            @updateProperties="handleUpdateProperties"
          />
        </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import LogicFlow from '@logicflow/core'
import { SelectionSelect, DndPanel, Menu } from '@logicflow/extension'
import '@logicflow/core/dist/index.css'
// import '@logicflow/extension/dist/index.css'  // 暂时注释CSS引入，避免路径错误

// 导入子组件
import MbLogicflowToolbar from './mb-logicflow-toolbar.vue'
import MbLogicflowSidebar from './mb-logicflow-sidebar.vue'
import MbLogicflowProperty from './mb-logicflow-property.vue'

// 导入自定义节点
import { registerCustomNodes } from './nodes/index.js'

export default {
  name: 'MbLogicflowDesign',
  components: {
    MbLogicflowToolbar,
    MbLogicflowSidebar,
    MbLogicflowProperty
  },
  props: {
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({ nodes: [], edges: [] })
    },
    // 是否显示属性面板
    showPropertyPanel: {
      type: Boolean,
      default: true
    },
    // 画布配置
    canvasConfig: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['save', 'clear', 'nodeSelect', 'edgeSelect'],
  setup(props, { emit }) {
    const canvasRef = ref(null)
    const logicFlow = ref(null)
    const selectedNodes = ref([])
    const selectedEdges = ref([])
    const properties = ref({})

    /**
     * 获取属性的方法（完全模仿MVP项目的$_getProperty）
     */
    const getProperty = () => {
      let mergedProperties = {}
      const { nodes, edges } = logicFlow.value?.getSelectElements() || { nodes: [], edges: [] }
      
      nodes.forEach(node => {
        mergedProperties = { ...mergedProperties, ...node.properties }
      })
      edges.forEach(edge => {
        mergedProperties = { ...mergedProperties, ...edge.properties }
      })
      
      // console.log('🔄 getProperty调用结果:', {
      //    选中元素数量: selectedElements.length,
      //    元素ID: elementId,
      //    原始属性: node?.properties || edge?.properties,
      //    处理后的属性: properties
      // })
      
      properties.value = mergedProperties
      return mergedProperties
    }

    /**
     * 显示右键菜单 - 参考organizer项目实现
     */
    const showContextMenu = (e, type, data) => {
      // 先清理已存在的菜单，避免多个菜单重叠
      const existingMenus = document.querySelectorAll('.lf-custom-menu')
      existingMenus.forEach(menu => {
        if (menu.parentNode) {
          menu.parentNode.removeChild(menu)
        }
      })

      // 创建菜单容器
      const menu = document.createElement('div')
      menu.className = 'lf-custom-menu'
      menu.style.position = 'fixed'
      menu.style.left = `${e.clientX}px`
      menu.style.top = `${e.clientY}px`
      menu.style.zIndex = '1000'
      menu.style.background = '#ffffff'
      menu.style.border = '1px solid #d9d9d9'
      menu.style.borderRadius = '6px'
      menu.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.12)'
      menu.style.padding = '6px 0'
      menu.style.minWidth = '100px'
      menu.style.maxWidth = '150px'

      let menuItems = []
      if (type === 'node') {
        menuItems = [
          { text: '🗑️ 删除', action: () => logicFlow.value.deleteNode(data.id) }
        ]
      } else if (type === 'edge') {
        menuItems = [
          { text: '🗑️ 删除', action: () => logicFlow.value.deleteEdge(data.id) }
        ]
      }

      menuItems.forEach(item => {
        const menuItem = document.createElement('div')
        menuItem.className = 'lf-custom-menu-item'
        menuItem.textContent = item.text
        menuItem.style.padding = '6px 12px'
        menuItem.style.fontSize = '14px'
        menuItem.style.color = '#333333'
        menuItem.style.cursor = 'pointer'
        menuItem.style.transition = 'all 0.2s'
        menuItem.style.lineHeight = '1.4'
        menuItem.style.whiteSpace = 'nowrap'

        if (item.text.includes('删除')) {
          menuItem.style.color = '#ff4d4f'
        }

        menuItem.addEventListener('mouseenter', () => {
          menuItem.style.backgroundColor = item.text.includes('删除') ? '#fff2f0' : '#f5f5f5'
        })

        menuItem.addEventListener('mouseleave', () => {
          menuItem.style.backgroundColor = 'transparent'
        })

        menuItem.addEventListener('click', () => {
          item.action()
          // 执行操作后立即清理菜单
          if (menu.parentNode) {
            menu.parentNode.removeChild(menu)
          }
        })

        menu.appendChild(menuItem)
      })

      // 点击其他地方关闭菜单
      const closeMenu = (event) => {
        if (menu.parentNode && !menu.contains(event.target)) {
          menu.parentNode.removeChild(menu)
          document.removeEventListener('click', closeMenu)
        }
      }

      document.body.appendChild(menu)
      setTimeout(() => {
        document.addEventListener('click', closeMenu)
      }, 0)
    }

    /**
     * 初始化LogicFlow
     */
    const initLogicFlow = async () => {
      await nextTick()
      
      if (!canvasRef.value) {
        // console.error('❌ 画布容器未找到')
        return
      }

      try {
        // 创建LogicFlow实例 - 参考organizer项目的方式
        const lf = new LogicFlow({
          container: canvasRef.value,
          // 基础配置
          width: canvasRef.value.offsetWidth,
          height: canvasRef.value.offsetHeight,
          // 网格配置
          grid: {
            visible: true,
            size: 20,
            type: 'dot'
          },
          // 背景配置
          background: {
            backgroundColor: '#fafafa'
          },
          // 键盘快捷键
          keyboard: {
            enabled: true
          },
          // 多选配置
          multipleSelectKey: 'ctrl',
          // 边的配置
          edgeType: 'polyline',
          // 插件配置 - 参考organizer项目
          plugins: [SelectionSelect, DndPanel, Menu],
          // 自定义配置
          ...props.canvasConfig
        })

        // 注册自定义节点
        registerCustomNodes(lf)

        // 设置主题
        lf.setTheme({
          // 节点样式
          baseNode: {
            strokeWidth: 2
          },
          // 边样式
          baseEdge: {
            strokeWidth: 2,
            stroke: '#1890ff'
          },
          // 文字样式
          nodeText: {
            fontSize: 12,
            fill: '#333333'
          },
          edgeText: {
            fontSize: 11,
            fill: '#666666'
          }
        })

        // 渲染初始数据
        lf.render(props.initialData)

        // 绑定事件监听
        bindEvents(lf)

        logicFlow.value = lf
        // console.log('✅ LogicFlow初始化成功')

      } catch (error) {
        // console.error('❌ LogicFlow初始化失败:', error)
      }
    }

    /**
     * 绑定事件监听
     */
    const bindEvents = (lf) => {
      // 统一的选中事件处理 - 参考mvp项目的实现
      lf.on('selection:selected,node:click,blank:click,edge:click', () => {
        nextTick(() => {
          const { nodes, edges } = lf.getSelectElements()
          
          // console.log('🎯 选中元素变化:', {
          //    选中数量: selectedElements.length,
          //    节点数量: nodes.length,
          //    边数量: edges.length,
          //    所有元素: selectedElements
          // })
          
          selectedNodes.value = nodes
          selectedEdges.value = edges
          
          // 立即调用getProperty更新properties（模仿MVP项目）
          getProperty()
          
          // 发送选中事件
          if (nodes.length > 0) {
            emit('nodeSelect', nodes[0])
            // console.log('🎯 选中节点:', {
            //    节点ID: nodeData.id,
            //    节点属性: nodeData.properties,
            //    原始数据: nodes[0]
            // })
          } else if (edges.length > 0) {
            emit('edgeSelect', edges[0])
            // console.log('🎯 选中边:', edges[0])
          } else {
            // console.log('🎯 取消选中')
          }
        })
      })

      // 右键菜单事件 - 参考organizer项目实现
      lf.on('node:contextmenu', ({ data, e }) => {
        e.preventDefault()
        console.log('🎯 节点右键:', data)
        showContextMenu(e, 'node', data)
      })

      lf.on('edge:contextmenu', ({ data, e }) => {
        e.preventDefault()
        console.log('🎯 连线右键:', data)
        showContextMenu(e, 'edge', data)
      })

      lf.on('blank:contextmenu', ({ e, position }) => {
        e.preventDefault()
        console.log('🎯 画布右键:', position)
        showContextMenu(e, 'blank', null)
      })

      // 节点拖拽完成事件
      lf.on('node:drop', ({ data }) => {
        // console.log('📍 节点拖拽完成:', {
        //    节点ID: data.id,
        //    新位置: { x: data.x, y: data.y },
        //    节点属性: data.properties
        // })
        // 自动选中功能将由统一的选中事件处理器处理
      })

      // 节点添加事件 - 拖拽后自动选中
      lf.on('node:add', ({ data }) => {
        // console.log('➕ 节点添加事件触发:', {
        //    节点类型: data.type,
        //    节点ID: data.id,
        //    节点属性: data.properties,
        //    完整数据: data
        // })
        
        // 关键：拖拽完成后自动选中新节点（参考MVP项目）
        nextTick(() => {
          // 清除现有选中状态
          lf.clearSelectElements()
          // 选中新添加的节点
          lf.selectElementById(data.id)
          // console.log('🎯 自动选中新节点:', data.id)
        })
      })

      // 连线完成事件
      lf.on('edge:add', ({ data }) => {
        // console.log('🔗 连线完成:', data)
      })
    }

    /**
     * 处理拖拽节点
     */
    const handleDragInNode = (nodeData) => {
      if (logicFlow.value) {
        logicFlow.value.dnd.startDrag(nodeData)
        // console.log('🎯 开始拖拽节点:', nodeData)
      }
    }

    /**
     * 处理保存
     */
    const handleSave = (graphData) => {
      emit('save', graphData)
    }

    /**
     * 处理清空
     */
    const handleClear = () => {
      selectedNodes.value = []
      selectedEdges.value = []
      emit('clear')
    }

    /**
     * 处理属性更新 - 完全参考mvp项目的$_setStyle实现
     */
    const handleUpdateProperties = (updateData) => {
      // console.log('📝 属性更新开始:', updateData)
      
      if (!logicFlow.value) {
        // console.error('❌ LogicFlow实例不存在')
        return
      }
      
      // 按照mvp项目的方式，对所有选中的节点和边更新属性
      selectedNodes.value.forEach(({ id }) => {
        logicFlow.value.setProperties(id, updateData.properties)
        // console.log('📝 更新节点属性:', id, updateData.properties)
      })
      
      selectedEdges.value.forEach(({ id }) => {
        logicFlow.value.setProperties(id, updateData.properties)
        // console.log('📝 更新边属性:', id, updateData.properties)
      })
      
             // 关键：立即重新获取属性数据 - 完全参考mvp的$_getProperty
       const refreshProperties = () => {
         const { nodes, edges } = logicFlow.value.getSelectElements()
         selectedNodes.value = nodes
         selectedEdges.value = edges
         
         let properties = {}
         nodes.forEach(node => {
           properties = { ...properties, ...node.properties }
         })
         edges.forEach(edge => {
           properties = { ...properties, ...edge.properties }
         })
         
         // console.log('🔄 重新获取的属性数据:', {
         //    选中元素: allSelectedElements,
         //    属性数据: elementProperties
         // })
         
         return properties
       }
       
       // 立即刷新属性
       refreshProperties()
       
       // 调用getProperty更新properties（模仿MVP项目）
       getProperty()
       
       // 尝试强制更新选中元素的显示（更轻量的方式）
       selectedNodes.value.forEach(({ id }) => {
         const nodeModel = logicFlow.value.getNodeModelById(id)
         if (nodeModel) {
           // console.log('🔄 强制更新节点样式:', id)
           // 触发重新计算样式
           nodeModel.getNodeStyle()
           nodeModel.getTextStyle()
         }
       })
    }

    /**
     * 获取图形数据
     */
    const getGraphData = () => {
      return logicFlow.value ? logicFlow.value.getGraphData() : null
    }

    /**
     * 设置图形数据
     */
    const setGraphData = (data) => {
      if (logicFlow.value) {
        logicFlow.value.render(data)
      }
    }

    /**
     * 窗口大小变化处理
     */
    const handleResize = () => {
      if (logicFlow.value && canvasRef.value) {
        logicFlow.value.resize(canvasRef.value.offsetWidth, canvasRef.value.offsetHeight)
      }
    }

    // 生命周期
    onMounted(() => {
      initLogicFlow()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      if (logicFlow.value) {
        logicFlow.value.destroy()
      }
    })

    // 暴露方法给父组件
    const designerRef = {
      getGraphData,
      setGraphData,
      logicFlow: () => logicFlow.value
    }

    return {
      canvasRef,
      logicFlow,
      selectedNodes,
      selectedEdges,
      handleDragInNode,
      handleSave,
      handleClear,
      handleUpdateProperties,
      designerRef,
      properties,
      getProperty
    }
  }
}
</script>

<style scoped>
.mb-logicflow-design {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  position: relative;
}

.design-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

/* 左侧面板 - 参考MVP项目：固定宽度，悬浮在画布上方 */
.sidebar-left {
  position: absolute;
  left: 0;
  top: 0;
  width: 280px;
  min-width: 280px;
  height: 100%;
  background: #ffffff;
  border-right: 1px solid #e8e8e8;
  overflow: hidden;
  z-index: 10;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

/* 画布容器 - 占满全部空间 */
.canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #ffffff;
}

.logic-flow-canvas {
  width: 100%;
  height: 100%;
}

/* 右侧面板 - 参考MVP项目：position absolute，悬浮在画布上方 */
.sidebar-right {
  position: absolute;
  right: 0;
  top: 0;
  width: 320px;
  min-width: 320px;
  max-width: 320px;
  height: 100%;
  background: #ffffff;
  border-left: 1px solid #e8e8e8;
  overflow: hidden;
  z-index: 10;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  /* 添加盒模型设置，确保边框不会影响内容宽度 */
  box-sizing: border-box;
  /* 确保面板内容不会水平溢出 */
  overflow-x: hidden;
}

/* LogicFlow样式覆盖 */
:deep(.lf-canvas-overlay) {
  background: transparent;
}

:deep(.lf-node-text) {
  user-select: none;
}

:deep(.lf-edge-text) {
  user-select: none;
}

/* 拖拽时的样式 */
:deep(.lf-dnd-shape) {
  opacity: 0.8;
}

/* 右键菜单样式 */
:deep(.lf-menu) {
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  padding: 6px 0;
  min-width: 100px;
  max-width: 150px;
  font-size: 14px;
  z-index: 1000;
}

:deep(.lf-menu-item) {
  padding: 6px 12px;
  font-size: 14px;
  color: #333333;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  display: flex;
  align-items: center;
  line-height: 1.4;
  white-space: nowrap;
}

:deep(.lf-menu-item:hover) {
  background-color: #f5f5f5;
}

:deep(.lf-menu-delete) {
  color: #ff4d4f;
}

:deep(.lf-menu-delete:hover) {
  background-color: #fff2f0;
  color: #ff4d4f;
}
</style> 