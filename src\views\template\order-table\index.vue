<template>
  <div class="main-container">
    <!-- 搜索区域 -->
    <mb-search style="margin-top: 8px;margin-bottom: -8px;" :where="tableOptions.where" @search="reloadTable"
      class="search-row" />

    <!-- 状态筛选标签页 -->
    <n-flex style="margin-bottom: 2px;">
      <template v-for="(filterCondition, index) in filterConditionSchema" :key="filterCondition.name">
        <n-tabs type="line" v-model:value="filterCondition.activeKey"
          @update:value="value => handleFilterTabChange(filterCondition.name, value)">
          <n-tab v-for="item in filterCondition.items" :key="item.field" :name="item.field">
            <n-badge :color="item.color" dot style="margin-right: 5px;" />
            {{ item.label + '(' + item.count + ')' }}
          </n-tab>
        </n-tabs>
        <n-divider v-if="index + 1 !== filterConditionSchema.length" vertical />
      </template>
    </n-flex>

    <!-- 工具栏 -->
    <div class="mb-toolbar" v-if="showToolbar">
      <n-space>
        <n-button :size="$global.uiSize.value" v-permission="'order:print'" type="primary" @click="handlePrintMaster"
          :disabled="!selectedOrderId" :loading="printLoading">
          <mb-icon icon="PrintOutline" />
          打印主单
        </n-button>
        <n-button :size="$global.uiSize.value" v-permission="'order:print'" type="primary" @click="handlePrintDetail"
          :disabled="!selectedOrderId" :loading="printLoading">
          <mb-icon icon="DocumentTextOutline" />
          打印明细
        </n-button>
        <n-button :size="$global.uiSize.value" v-permission="'order:edit'" type="info" @click="handleEdit"
          :disabled="!selectedOrderId">
          <mb-icon icon="CreateOutline" />
          编辑订单
        </n-button>
      </n-space>
    </div>

    <!-- 主表格 -->
    <mb-table ref="table" v-bind="tableOptions" style="flex: 1" @selectedRow="orderRowChanged"
      :loading="tableLoading" />

    <!-- 订单详情抽屉 -->
    <n-drawer v-model:show="showDrawer" width="96%" :height="800" placement="right" :on-after-enter="onDrawerOpen">
      <n-drawer-content title="送货单查看" closable>

        <!-- 原先订单详情Tab中的内容移动到这里 -->
        <n-form class="order-form" label-placement="left" label-width="80" :model="bill.master">
          <n-form-item class='form-item-wrapper' label="送货单号">
            <n-input class='item-input' v-model:value="bill.master.BillNum" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="单据日期">
            <n-input class='item-input' v-model:value="bill.master.BillDate" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="单据类型">
            <n-input class='item-input' v-model:value="bill.master.pbillType" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="供应商">
            <n-input class='item-input' v-model:value="bill.master.suppName" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="采购日期">
            <n-input class='item-input' v-model:value="bill.master.pbillDate" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="状态">
            <n-input class='item-input' v-model:value="bill.master.mesStatus" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="是否使用">
            <n-input class='item-input' v-model:value="bill.master.actived" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="备注">
            <n-input class='item-input' v-model:value="bill.master.memo" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="创建人">
            <n-input class='item-input' v-model:value="bill.master.maker" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="创建日期">
            <n-input class='item-input' v-model:value="bill.master.makedate" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="修改人">
            <n-input class='item-input' v-model:value="bill.master.modifyby" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="修改日期">
            <n-input class='item-input' v-model:value="bill.master.modifydate" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="工厂">
            <n-input class='item-input' v-model:value="bill.master.factroy" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="公司">
            <n-input class='item-input' v-model:value="bill.master.company" readonly />
          </n-form-item>
        </n-form>
        <n-divider />

        <n-form class="order-form" label-placement="left" label-width="100" :model="curDetailRow">
          <!-- 采购订单号 -->
          <n-form-item class="form-item-wrapper" label="采购订单号">
            <!-- <n-input class="item-input" v-model:value="curDetailRow.billnum" />-->

            <mb-select-table v-bind="editorTableFormOptions" v-model="curDetailRow.billnum" :search="searchOptions" />

          </n-form-item>

          <!-- 采购订单行号 -->
          <n-form-item class="form-item-wrapper" label="采购订单行号">
            <n-input class="item-input" v-model:value="curDetailRow.purorderseq" />
          </n-form-item>

          <!-- 物料编码 -->
          <n-form-item class="form-item-wrapper" label="物料编号">
            <n-input class="item-input" v-model:value="curDetailRow.itemno" />
          </n-form-item>

          <!-- 物料名称 -->
          <n-form-item class="form-item-wrapper" label="物料名称">
            <n-input class="item-input" v-model:value="curDetailRow.itemName" />
          </n-form-item>

          <!-- 规格型号 -->
          <n-form-item class="form-item-wrapper" label="规格型号">
            <n-input class="item-input" v-model:value="curDetailRow.itemModel" />
          </n-form-item>

          <!-- 单位 -->
          <n-form-item class="form-item-wrapper" label="单位">
            <n-input class="item-input" v-model:value="curDetailRow.itemUnit" />
          </n-form-item>

          <!-- 已送货数量 -->
          <n-form-item class="form-item-wrapper" label="可送货数量">
            <n-input-number class="item-input" v-model:value="curDetailRow.deliverablequan" />
          </n-form-item>

          <!-- 待送货数量 -->
          <n-form-item class="form-item-wrapper" label="送货数量">
            <n-input-number class="item-input" v-model:value="curDetailRow.quan" />
          </n-form-item>


          <!-- 急料标识 -->
          <n-form-item class="form-item-wrapper" label="急料标识">
            <n-select v-model:value="curDetailRow.urgentflag" :options="[
              { label: '非急料', value: '非急料' }, { label: '急料', value: '急料' }
            ]" />
          </n-form-item>
        </n-form>

        <!-- 明细表格 -->
        <mb-editor-table ref="detailTableRef" v-bind="detailTableOptions" style="flex: 1; margin-bottom: 16px;"
          @selectedRow="selectDetailRow" />

        <!-- 操作按钮 -->
        <n-space justify="end" style="margin-top: 16px;">
          <n-button @click="showDrawer = false">
            取消
          </n-button>
          <n-button type="primary" @click="saveOrderData" :loading="printLoading">
            保存
          </n-button>
          <n-button type="info" @click="handlePrintMaster" :loading="printLoading">
            <mb-icon icon="PrintOutline" />
            打印主单
          </n-button>
          <n-button type="success" @click="handlePrintDetail" :loading="printLoading">
            <template #icon>
              <mb-icon icon="DocumentTextOutline" />
            </template>
            打印明细
          </n-button>
        </n-space>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup>

import { ref, reactive, computed, watch } from 'vue'
import { useMessage, useDialog, useLoadingBar } from 'naive-ui'

// ==================== 响应式状态管理 ====================
const $message = useMessage()
const $dialog = useDialog()
const $loadingBar = useLoadingBar()

// 控制组件显示状态
const showDrawer = ref(false)
const showToolbar = ref(true)
const tableLoading = ref(false)
const printLoading = ref(false)

// 订单相关状态
const orderRowId = ref(null)
const selectedOrderId = computed(() => orderRowId.value)
const detailTableRef = ref()
const table = ref()

// ==================== 事件处理函数 ====================
// 订单主表选中行发生变化时更新 ID
const orderRowChanged = (row) => {
  orderRowId.value = row?.sguid || null
  console.log('选中订单ID:', orderRowId.value)
}

// tab页筛选条件配置
const filterConditionSchema = reactive([
  {
    name: 'mesStatus',
    activeKey: 'all', // 推荐将默认激活的key设置为 'all'
    items: [
      {
        field: 'all',
        label: '全部',
        color: 'grey', // 使用一个柔和的灰色或中性色来表示“全部”，不赋予具体状态含义
        count: 0,
      },
      {
        field: '0',
        label: '未审核',
        color: 'red', // 红色，表示待处理、未完成、需要关注
        count: 0,
      },
      {
        // ?? 重要的修正：假设 '已审核' 的 field 是 '1'。请根据你的实际业务调整！
        field: '1',
        label: '已审核',
        color: 'green', // 绿色，表示审核通过、状态良好
        count: 0,
      },
      {
        field: '2',
        label: '出货中',
        color: 'blue', // 蓝色，表示进行中的状态
        count: 0,
      },
      {
        // ?? 重要的修正：假设 '已出货' 的 field 是 '20'。请根据你的实际业务调整！
        field: '3',
        label: '已出货',
        color: 'green', // 绿色，表示出货流程已最终完成
        count: 0,
      },
    ],
  }])

// ==================== 数据状态管理 ====================
const detailData = ref([])
const bill = reactive({
  master: {},
  detail00: []
})
const curDetailRow = ref({})

// ==================== 数据获取和处理函数 ====================
// 获取订单详情数据，并打开抽屉
const fetchDetail = async () => {
  if (!orderRowId.value) {
    $message.warning("请先选择一行数据")
    return
  }

  try {
    $loadingBar.start()
    const res = await $common.get('/srm/outstock/loadbill', {
      sguid: orderRowId.value
    })

    if (res.data) {
      bill.master = res.data.master || {}
      const details = res.data.detail00 || []

      if (Array.isArray(details) && details.length > 0) {
        curDetailRow.value = { ...details[0] }
      } else {
        curDetailRow.value = {}
      }

      detailData.value = details
      showDrawer.value = true
      $loadingBar.finish()
      $message.success("订单详情加载成功")
    }
  } catch (error) {
    console.error("加载订单详情失败:", error)
    $message.error("加载订单详情失败")
    $loadingBar.error()
  }
}

const onDrawerOpen = () => {
  detailTableRef.value?.setData(detailData.value)
}

// 添加新的功能函数
const handleEdit = () => {
  if (!orderRowId.value) {
    $message.warning("请先选择一行数据")
    return
  }
  fetchDetail()
}

const handlePrintMaster = async () => {
  if (!orderRowId.value) {
    $message.warning("请先选择一行数据")
    return
  }

  try {
    printLoading.value = true
    await printOrderMaster()
    $message.success("主单打印成功")
  } catch (error) {
    console.error("打印失败:", error)
    $message.error("打印失败")
  } finally {
    printLoading.value = false
  }
}

const handlePrintDetail = async () => {
  if (!orderRowId.value) {
    $message.warning("请先选择一行数据")
    return
  }

  try {
    printLoading.value = true
    await printOrderDetail()
    $message.success("明细打印成功")
  } catch (error) {
    console.error("打印失败:", error)
    $message.error("打印失败")
  } finally {
    printLoading.value = false
  }
}

// ==================== 打印功能实现 ====================
const printOrderMaster = async () => {
  if (!bill.master || !Object.keys(bill.master).length) {
    await fetchDetail()
  }

  // 创建打印内容HTML
  const printContent = createMasterPrintContent()

  // 使用浏览器原生打印
  const printWindow = window.open('', '_blank')
  printWindow.document.write(printContent)
  printWindow.document.close()
  printWindow.print()
  printWindow.close()
}

const printOrderDetail = async () => {
  if (!detailData.value || !detailData.value.length) {
    await fetchDetail()
  }

  // 创建明细打印内容HTML
  const printContent = createDetailPrintContent()

  // 使用浏览器原生打印
  const printWindow = window.open('', '_blank')
  printWindow.document.write(printContent)
  printWindow.document.close()
  printWindow.print()
  printWindow.close()
}

// ==================== 表格配置 ====================
const tableOptions = ref({
  url: '/srm/outstock/outstockquery',
  page: true,
  where: {
    billnum: {
      label: '采购单号',
      component: 'input',
      props: {
        clearable: true,
        placeholder: '请输入采购单号'
      }
    },
    pbillDate: {
      label: '日期',
      component: 'date',
      props: {
        type: 'daterange',
        clearable: true,
        width: '400px'
      },
      width: '400px'
    },
    suppName: {
      label: '供应商',
      component: 'input',
      props: {
        clearable: true,
        placeholder: '请输入供应商'
      }
    },
  },
  cols: [
    {
      "field": "billnum",
      "label": "采购单号",
      "width": 120
    },
    {
      "field": "billdate",
      "label": "单据日期",
      "width": 120
    },
    {
      "field": "pbillType",
      "label": "单据类型",
      "width": 120
    },
    {
      "field": "suppName",
      "label": "供应商",
      "width": 220
    },
    {
      "field": "pbillDate",
      "label": "采购日期",
      "width": 120
    },
    {
      "field": "mesStatus",
      "label": "状态",
      "width": 120
    },
    {
      "field": "actived",
      "label": "是否使用",
      "width": 120
    },
    {
      "field": "memo",
      "label": "备注",
      "width": 120
    },
    {
      "field": "maker",
      "label": "创建人",
      "width": 200
    },
    {
      "field": "makedate",
      "label": "创建日期",
      "width": 120
    },
    {
      "field": "modifyby",
      "label": "修改人",
      "width": 180
    },
    {
      "field": "modifydate",
      "label": "修改日期",
      "width": 120
    },
    {
      "field": "factroy",
      "label": "工厂",
      "width": 120
    },
    {
      "field": "company",
      "label": "公司",
      "width": 120
    }
  ],
  // 双击事件，用于弹出抽屉
  onDblclick: ({ row }) => {
    orderRowId.value = row.sguid
    fetchDetail()
  },
  afterLoadData: (data) => {
    // 检查 data.filterCondition 是否存在
    if (data && data.filterCondition) {
      // 遍历 filterConditionSchema 中的每个筛选类别（如 mesStatus, orderDate）
      filterConditionSchema.forEach(filterCategory => {
        const categoryName = filterCategory.name; // 获取类别名称，如 'mesStatus' 或 'orderDate'
        const counts = data.filterCondition[categoryName]; // 尝试从后端数据中获取该类别的计数
        if (counts) { // 如果后端返回了该类别的计数数据
          // 遍历当前 filterCategory 的所有 items
          filterCategory.items.forEach(item => {
            // 如果后端数据中有对应 field 的计数，则更新 count，否则默认为 0
            item.count = counts[item.field] !== undefined ? counts[item.field] : 0;
          });
        } else {
          // 如果后端没有返回此 categoryName 的任何数据，将所有 count 重置为 0
          filterCategory.items.forEach(item => {
            item.count = 0;
          });
        }
      });
    } else {
      // 如果 data.filterCondition 不存在，所有计数都重置为 0
      filterConditionSchema.forEach(filterCategory => {
        filterCategory.items.forEach(item => {
          item.count = 0;
        });
      });
    }
  }
})

// ==================== 筛选和表格操作函数 ====================
const handleFilterTabChange = (filterName, value) => {
  console.log(`筛选条件变更: ${filterName} = ${value}`)

  if (filterName === 'mesStatus') {
    if (!tableOptions.value.where.mesStatus) {
      tableOptions.value.where.mesStatus = {}
    }

    if (value === 'all') {
      delete tableOptions.value.where.mesStatus.value
    } else {
      tableOptions.value.where.mesStatus.value = value
    }
  }
  reloadTable()
}

const reloadTable = () => {
  tableLoading.value = true
  table.value?.reload()
  setTimeout(() => {
    tableLoading.value = false
  }, 500)
}

// ==================== 打印内容创建函数 ====================
const createMasterPrintContent = () => {
  const master = bill.master
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>送货单</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; font-size: 24px; font-weight: bold; margin-bottom: 30px; }
        .info-row { display: flex; margin-bottom: 10px; }
        .info-item { flex: 1; padding: 5px; }
        .label { font-weight: bold; }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">送货单</div>
      <div class="info-row">
        <div class="info-item">
          <span class="label">送货单号:</span> ${master.BillNum || ''}
        </div>
        <div class="info-item">
          <span class="label">单据日期:</span> ${master.BillDate || ''}
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="label">供应商:</span> ${master.suppName || ''}
        </div>
        <div class="info-item">
          <span class="label">状态:</span> ${master.mesStatus || ''}
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="label">单据类型:</span> ${master.pbillType || ''}
        </div>
        <div class="info-item">
          <span class="label">采购日期:</span> ${master.pbillDate || ''}
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="label">备注:</span> ${master.memo || ''}
        </div>
        <div class="info-item">
          <span class="label">创建人:</span> ${master.maker || ''}
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="label">公司:</span> ${master.company || ''}
        </div>
        <div class="info-item">
          <span class="label">工厂:</span> ${master.factroy || ''}
        </div>
      </div>
    </body>
    </html>
  `
}

const createDetailPrintContent = () => {
  const master = bill.master
  const details = detailData.value || []

  const detailRows = details.map(item => `
    <tr>
      <td>${item.itemno || ''}</td>
      <td>${item.itemName || ''}</td>
      <td>${item.itemModel || ''}</td>
      <td>${item.quan || ''}</td>
      <td>${item.itemUnit || ''}</td>
      <td>${item.urgentflag || ''}</td>
      <td>${item.RemarkSub || ''}</td>
    </tr>
  `).join('')

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>送货明细单</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; font-size: 24px; font-weight: bold; margin-bottom: 20px; }
        .master-info { margin-bottom: 20px; }
        .info-row { display: flex; margin-bottom: 8px; }
        .info-item { flex: 1; padding: 3px; }
        .label { font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; }
        th { background-color: #f0f0f0; font-weight: bold; }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">送货明细单</div>
      <div class="master-info">
        <div class="info-row">
          <div class="info-item">
            <span class="label">送货单号:</span> ${master.BillNum || ''}
          </div>
          <div class="info-item">
            <span class="label">供应商:</span> ${master.suppName || ''}
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">单据日期:</span> ${master.BillDate || ''}
          </div>
          <div class="info-item">
            <span class="label">状态:</span> ${master.mesStatus || ''}
          </div>
        </div>
      </div>
      <table>
        <thead>
          <tr>
            <th>物料编号</th>
            <th>物料名称</th>
            <th>规格型号</th>
            <th>数量</th>
            <th>单位</th>
            <th>急料标识</th>
            <th>备注</th>
          </tr>
        </thead>
        <tbody>
          ${detailRows}
        </tbody>
      </table>
    </body>
    </html>
  `
}

// ==================== 数据监听和响应式处理 ====================
// 监听明细行数据变化，同步更新到明细表格
watch(() => curDetailRow.value, (newValue) => {
  if (newValue && newValue.sguid) {
    const index = detailData.value.findIndex(item => item.sguid === newValue.sguid)
    if (index !== -1) {
      detailData.value.splice(index, 1, { ...newValue })
      detailTableRef.value?.setData(detailData.value)
    }
  }
}, { deep: true })

const searchOptions = reactive({
  fields: ['BillNum']
})

const editorTableFormOptions = ref({
  height: 400,
  width: 800,
  multiple: false,
  showsearch: false,
  onSelectData({ selectData }) {
    if (selectData) {
      curDetailRow.value.itemNo = selectData.itemNo
      curDetailRow.value.itemName = selectData.itemName
      curDetailRow.value.itemModel = selectData.itemModel
      curDetailRow.value.rowno = selectData.rowno
      curDetailRow.value.billnum = selectData.billnum
    }
  },
  tableOptions: {
    url: '/srm/datasource/poorderDropdownList',
    page: true,
    selection: false,
    cols: [
      { field: 'billnum', label: '采购订单号', width: 100 },
      { field: 'rowno', label: '采购订单行号', width: 80 },
      { field: 'itemNo', label: '物料编号', width: 100 },
      { field: 'itemName', label: '物料名称', width: 100 },
      { field: 'itemModel', label: '规格型号', width: 120 },
      { field: 'itemUnit', label: '单位', width: 60 },
      { field: 'quantity', label: '订单数量', width: 80 },
      { field: 'quan', label: '送货数量', width: 80 },
      { field: 'fqty', label: '待送货数量', width: 80 },
      { field: 'needdate', label: '需求日期', width: 80 },
      { field: 'suppName', label: '供应商名称', width: 150 }
    ]
  }
})

const editorTableOptions = ref({
  height: 400,
  width: 800,
  multiple: false,
  onSelectData({ selectData, editorCurrentRow }) {
    console.log('选中数据:', selectData)
    console.log('当前编辑行:', editorCurrentRow)

    if (selectData && editorCurrentRow) {
      editorCurrentRow.itemNo = selectData.itemNo
      editorCurrentRow.itemName = selectData.itemName
      editorCurrentRow.itemModel = selectData.itemModel
      editorCurrentRow.rowno = selectData.rowno
      editorCurrentRow.billnum = selectData.billnum
    }
  },
  search: { fields: ['BillNum'] },
  showsearch: false,
  tableOptions: {
    url: '/srm/datasource/poorderDropdownList',
    page: true,
    selection: false,
    cols: [
      { field: 'billnum', label: '采购订单号', width: 100 },
      { field: 'rowno', label: '采购订单行号', width: 80 },
      { field: 'itemNo', label: '物料编号', width: 100 },
      { field: 'itemName', label: '物料名称', width: 100 },
      { field: 'itemModel', label: '规格型号', width: 120 },
      { field: 'itemUnit', label: '单位', width: 60 },
      { field: 'quantity', label: '订单数量', width: 80 },
      { field: 'quan', label: '送货数量', width: 80 },
      { field: 'fqty', label: '待送货数量', width: 80 },
      { field: 'needdate', label: '需求日期', width: 80 },
      { field: 'suppName', label: '供应商名称', width: 150 }
    ]
  }
})

const detailTableOptions = ref({
  page: false,
  cols: [
    {
      field: 'billnum', label: '采购单号', width: 120, component: 'select-table',
      componentProps: editorTableOptions.value,
      componentStyle: "height:100%", alwaysEdit: false
    },
    { field: 'purorderseq', label: '采购订单行号', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'itemno', label: '物料编号', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'itemName', label: '物料名称', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'itemModel', label: '规格型号', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'itemUnit', label: '单位', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'deliverablequan', label: '可送货数量', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'quan', label: '送货数量', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    {
      field: 'urgentflag', label: '急料标识', width: 120, component: 'select',
      componentProps: {
        options: [
          { label: '非急料', value: '非急料' }, { label: '急料', value: '急料' }
        ]
      },
      componentStyle: "height:100%", alwaysEdit: false
    },
    { field: 'RemarkSub', label: '备注', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false }

  ]
})

const selectDetailRow = (row) => {
  console.log('选中了明细行', row)
  curDetailRow.value = row
}

// 保存订单数据
const saveOrderData = async () => {
  if (!bill.master || !Object.keys(bill.master).length) {
    $message.warning("请先选择订单")
    return
  }

  try {
    $loadingBar.start()
    const saveData = {
      master: bill.master,
      detail: detailData.value
    }

    const res = await $common.post('/srm/outstock/save', saveData)
    if (res.success) {
      $message.success("保存成功")
      showDrawer.value = false
      reloadTable()
    } else {
      $message.error(res.message || "保存失败")
    }
    $loadingBar.finish()
  } catch (error) {
    console.error("保存失败:", error)
    $message.error("保存失败")
    $loadingBar.error()
  }
}
</script>

<style scoped>
.main-container {
  width: 100%;
  height: 100%;
  display: inline-flex;
  flex-direction: column;
}

.header-row {
  height: fit-content;
  padding: 16px 0;
}

.search-row {
  padding-bottom: 12px;
}

/* 抽屉内部的表单样式，与原先 Tab 内部的样式保持一致 */

.order-form {
  .form-item-wrapper {
    width: 320px;
    display: inline-flex;
    margin: 4px 8px;

    .n-form-item-blank {
      flex: 1;
      color: red;
    }

    .item-input {
      flex: 1;
    }
  }
}

:deep(.magic-select-table > .mb-list) {
  padding: 1px;
}
</style>
<style>
.n-tab-pane {
  display: inline-flex;
  flex-direction: column;
}

.n-tabs-pane-wrapper {
  flex: 1;
  width: 100%;
}

.n-form-item-blank {
  flex: 1;
}
</style>