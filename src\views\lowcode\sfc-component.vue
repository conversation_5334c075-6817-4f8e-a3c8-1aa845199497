<style scoped>
.tools i,
.tools svg{
    cursor: pointer;
}
.logo-content {
    text-align: center;
    width: 90%;
    max-width: 400px;
    margin: 0 auto;
    padding-top: 25vh;
}
.title-bg{
    background: -webkit-linear-gradient(315deg, #42d392 25%, #647eff);
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1.25;
}
.code-editor-tabs :deep(.n-tabs-nav) {
  padding: 0 4px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.editor-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  height: 100%;
}

.editor-toolbar {
  background-color: #fff;
  padding: 4px 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.editor-btn {
  min-width: 80px;
}

.editor-content {
  flex: 1;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  position: relative;
  height: calc(100% - 38px);
}

.editor-actions {
  position: absolute;
  top: 6px;
  right: 6px;
  z-index: 10;
  display: flex;
  gap: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2px;
  border-radius: 4px;
}

.n-split :deep(.n-split-pane-1) {
  overflow-y: auto;
  overflow-x: hidden;
}

.history-drawer .compare {
  padding-left: 12px;
}

/* 优化树形控件样式 */
.mb-tree-container :deep(.n-tree) {
  font-size: 13px;
}

.mb-tree-container :deep(.n-tree .n-tree-node-content) {
  padding: 3px 6px;
}

/* 优化标签页样式 */
.code-editor-tabs :deep(.n-tabs-tab) {
  padding: 4px 12px;
  font-size: 13px;
}

.code-editor-tabs :deep(.n-tabs-tab-close) {
  margin-left: 6px;
}
</style>

<style>
.n-config-provider{
    background-color: white!important;
}
body{
    font: 14px var(--van-base-font)!important;
}
</style>

<template>
    <div class="h-full">
        <n-split
            direction="horizontal"
            style="height: 100%"
            :default-size="0.15"
            :max="0.3"
            :min="0.1"
        >
            <template #1>
                <div class="h-full mb-tree-container">
                    <mb-tree
                        ref="treeRef"
                        url="/system/component/tree?source=0"
                        :expand="false"
                        search
                        search-width="100%"
                        :checked="false"
                        :checkable="false"
                        show-line
                        @node-click="nodeClick"
                        :contextmenu="treeContextmenu"
                        :icon="{ expand: 'FolderOpenOutline', collapse: 'Folder', node: 'LogoVue' }"
                    />
                    <mb-modal ref="nameModal" :title="updateComponent ? '修改': '添加'" @confirm="saveComponent">
                        <n-form :size="$global.uiSize.value" ref="dataForm" :rules="rules" :model="formData" label-placement="left" label-width="60px">
                            <n-form-item label="名称" path="name">
                                <n-input v-model:value="formData.name"/>
                            </n-form-item>
                            <n-form-item label="备注" path="remark">
                                <n-input v-model:value="formData.remark"/>
                            </n-form-item>
                        </n-form>
                    </mb-modal>
                </div>
            </template>
            <template #2>
                <div style="padding: 4px; height: 100%; box-sizing: border-box;">
                    <div class="logo-content" v-if="tabs.length == 0">
                        <div class="text-6xl mt-0 mb-0 italic font-black title-bg">
                            {{ $global.title }}
                        </div>
                        <p class="text-base" style="color: #213547">【转型很遥远，我们一点一点做，它就会离你越来越近】</p>
                        <n-button type="primary" color="#42b883" @click="createFile('0', 0)">新建分组</n-button>
                        <div class="flex flex-wrap justify-between mt-4" style="color: #b6b6b6">
                            <div class="text-base w-1/2 mb-3">保存Ctrl + S</div>
                            <div class="text-base w-1/2">撤销Ctrl + Z</div>
                            <div class="text-base w-1/2">反撤销Ctrl + Shift + Z</div>
                        </div>
                    </div>
                    <n-tabs
                        v-else
                        v-model:value="currentNodeId"
                        type="card"
                        closable
                        tab-style="min-width: 70px; padding: 4px 10px;"
                        @close="tabClose"
                        class="h-full w-full code-editor-tabs"
                    >
                        <n-tab-pane
                            v-for="tab in tabs"
                            :key="tab.id"
                            :tab="tab.name + (tab.isSave && tab.isSave == 1 ? ' *' : '')"
                            :name="tab.id"
                            display-directive="show"
                            class="h-full w-full"
                            style="padding: 0"
                        >
                            <div class="flex flex-col h-full">
                                <div class="flex-1">
                                    <div class="h-full w-full editor-container">
                                        <div class="editor-toolbar">
                                            <div class="flex items-center gap-1">
                                                <n-button 
                                                    :type="editorType === 'code' ? 'primary' : 'default'" 
                                                    @click="editorType = 'code'"
                                                    class="editor-btn"
                                                    size="small"
                                                >
                                                    代码编辑
                                                </n-button>
                                                <n-button 
                                                    :type="editorType === 'amis' ? 'primary' : 'default'" 
                                                    @click="editorType = 'amis'"
                                                    class="editor-btn"
                                                    size="small"
                                                >
                                                    AMIS编辑
                                                </n-button>
                                            </div>
                                            
                                            <div class="text-xs text-gray-500 ml-3 flex items-center gap-1">
                                                当前渲染方式：{{ currentNode?.dynamicMode == '1' ? '动态代码' : 'AMIS' }}
                                                <n-button 
                                                    type="primary" 
                                                    size="tiny" 
                                                    @click="updateDynamicMode('1')" 
                                                    v-if="currentNode?.dynamicMode == '2'"
                                                >
                                                    使用动态代码渲染
                                                </n-button>
                                                <n-button 
                                                    type="primary" 
                                                    size="tiny" 
                                                    @click="updateDynamicMode('2')" 
                                                    v-if="currentNode?.dynamicMode == '1'"
                                                >
                                                    使用AMIS渲染
                                                </n-button>
                                            </div>
                                        </div>
                                        
                                        <div v-show="editorType === 'code'" class="h-full w-full editor-content">
                                            <div class="editor-actions">
                                                <n-button type="primary" size="tiny" @click="saveCode()">
                                                    <template #icon>
                                                        <mb-icon icon="SaveSharp" color="#fff" />
                                                    </template>
                                                </n-button>

                                                <n-button type="primary" secondary size="tiny" @click="openHistory(tabs.filter(it => it.id == currentNodeId)[0])">
                                                    <template #icon>
                                                        <mb-icon icon="History24Filled" color="#2d8cf0" />
                                                    </template>
                                                </n-button>
                                            </div>

                                            <mb-monaco-volar
                                                :ref="(el) => setComponentRef(el, tab.id)"
                                                :code="tab.code"
                                                :file-name="tab.id"
                                                @didChangeModelContent="onDidChangeModelContent(tab)"
                                                @save="saveCode()"
                                            />
                                        </div>
                                        <div v-show="editorType === 'amis'" class="h-full w-full editor-content">
                                            <iframe 
                                                frameborder="0" 
                                                :src="`https://amiseditor.zhiyoutop.com/#/edit/0?id=${tab.id}&tenantId=${tenantId}`" 
                                                style="width: 100%; height: 100%;"
                                            ></iframe>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </n-tab-pane>
                    </n-tabs>
                </div>
            </template>
        </n-split>
        <mb-modal ref="errorInfoModal" width="900px" title="错误信息" :show-footer="false">
            <textarea v-model="errorInfo" :rows="40" style="width: 100%" />
        </mb-modal>
        <n-drawer
            v-model:show="showDrawer"
            :default-height="800"
            placement="bottom"
            resizable
            @after-leave="drawerClose"
            class="history-drawer"
        >
            <n-drawer-content closable :title="currentNode?.name + ' - 历史记录'">
                <div class="flex h-full">
                    <div style="width: 380px; height: 100%; overflow: auto;" class="h-full">
                        <n-data-table
                            :columns="tableColumns"
                            :data="tableData"
                            :row-props="tableRowProps"
                            size="small"
                        />
                    </div>
                    <div class="compare flex-1 h-full">
                        <div class="flex flex-col h-full">
                            <div class="flex items-center">
                                <div class="flex-1 flex items-center">
                                    <div class="flex-1">
                                        {{ currentHistoryOldDate }} by {{ currentHistoryOldCreateBy }}
                                    </div>
                                    <div style="padding: 0px 40px 5px 0px">
                                        <n-button type="primary" size="small" @click="restoreToThisVersion">还原到此版本</n-button>
                                    </div>
                                </div>
                                <div class="flex-1">当前版本</div>
                            </div>
                            <mb-monaco-volar
                                compare
                                ref="historyEditorRef"
                                :code="historyCode"
                                :old-code="historyOldCode"
                                :file-name="currentNodeId"
                            />
                        </div>
                    </div>
                </div>
            </n-drawer-content>
        </n-drawer>
    </div>
</template>

<script setup>
import { ref, reactive, onBeforeMount, computed } from 'vue'
import {compileCode} from "@/scripts/compiler/sfc-compiler";
import {setupMonacoVolar} from "@/scripts/plugins/monacoVolar";
import {useUserStore} from "@/store/modules/userStore";
onBeforeMount(async () => {
    await setupMonacoVolar()
})
const monacoVolarRefs = reactive({})
const nameModal = ref()
const formData = reactive({
    remark: '',
    name: '',
    type: 0,
    source: 0
})
const userStore = useUserStore()
const tenantId = userStore.getTenantId()
const currentNodeId = ref()
const currentNode = computed(() => tabs.value.filter(it => it.id == currentNodeId.value)[0])
const treeRef = ref()
const errorInfoModal = ref()
const errorInfo = ref()
const updateComponent = ref(false)
const showDrawer = ref(false)
const historyEditorRef = ref()
const historyCode = ref()
const historyOldCode = ref()
const historyTableWhere = reactive({})
const currentHistoryOldDate = ref()
const currentHistoryOldCreateBy = ref()
const editorType = ref('code')
const tableColumns = reactive([{
    title: '时间',
    key: 'createDate'
}, {
    title: '操作人',
    key: 'createBy',
    width: 150
}])
const tableData = ref()
let currentRowDom = null
const tableRowProps = (row) => {
    let _rowProps = {}
    _rowProps['onClick'] = (e) => {
        let setBackgroundColor = (dom, color) => {
            dom.forEach(d => {
                let _color = color
                if(d){
                    if(!_color){
                        _color = ''
                    }
                    d.querySelectorAll('td').forEach(it => {
                        it.style['background-color'] = _color
                    })
                }
            })
        }
        if(currentRowDom){
            setBackgroundColor([currentRowDom, currentRowDom.previousElementSibling, currentRowDom.nextElementSibling])
        }
        currentRowDom = e.currentTarget
        setBackgroundColor([currentRowDom], "#D9DDE2")
        historyTableSelect(row)
    }
    return _rowProps
}
function historyTableSelect(row){
    currentHistoryOldDate.value = row.createDate
    currentHistoryOldCreateBy.value = row.createBy
    $common.get('/system/component/history/detail', { id: row.id }).then(res => {
        historyOldCode.value = res.data
    })
}
function drawerClose(){
    historyEditorRef.value.dispose()
}
const treeContextmenu = ref([{
    key: 'addGroup',
    label: '添加分组',
    click: (node) => {
        createFile(node.id, 0)
    }
}, {
    key: 'addComponent',
    label: '添加组件',
    click: (node) => {
        createFile(node.id, 1)
    }
}, {
    key: 'updateComponent',
    label: '修改',
    click: (node) => {
        formData.remark = ''
        formData.type = node.isGroup === 0 ? 1 : 0
        if(node.name.indexOf('(') !== -1){
            formData.name = analyzeName(node.name, true)
            formData.remark = analyzeName(node.name, false)
        }else{
            formData.name = node.name
        }
        updateComponent.value = true
        currentNodeId.value = node.id
        nameModal.value.show()
    }
}, {
    key: 'delete',
    label: '删除',
    click: (node) => {
        $common.handleDelete({
            url: '/system/component/delete',
            id: node.id,
            done(){
                treeRef.value.reload()
            }
        })
    }
}, {
    key: 'history',
    label: '历史记录',
    if(node){
        return !node.isGroup;
    },
    click: (node) => {
        openHistory(node)
    }
}])
function openHistory(node){
    currentNodeId.value = node.id
    showDrawer.value = true
    historyTableWhere.componentId = node.id
    historyTableWhere.size = 99999999
    $common.post('/system/component/history', historyTableWhere).then(res => {
        tableData.value = res.data.list
    })
    $common.get('/system/component/getLastCode', { componentId: node.id }).then(res => {
        if(res.data){
            const { sourceCode, createDate, createBy } = res.data
            currentHistoryOldDate.value = createDate
            currentHistoryOldCreateBy.value = createBy
            historyOldCode.value = sourceCode
            if(monacoVolarRefs[currentNodeId.value]){
                historyCode.value = monacoVolarRefs[currentNodeId.value].getValue()
            }else{
                historyCode.value = sourceCode
            }
        }
    })
}
// 解析name
function analyzeName(str, outside){
    // 获取括号外 or 括号内
    const regexStr = outside ? /(.*?)(?=\()/ : /\((.*?)\)/;
    const match = str.match(regexStr);
    if (match) {
        return outside ? match[0].trim() : match[1];
    }
}
function createFile(id, type){
    formData.type = type
    formData.name = ''
    formData.remark = ''
    updateComponent.value = false
    currentNodeId.value = id
    nameModal.value.show()
}
function saveComponent(){
    dataForm.value.validate((errors) => {
        if (!errors) {
            $common.post('/system/component/save/tree',{
                [updateComponent.value ? 'id' : 'pid']: currentNodeId.value,
                ...formData
            }).then((res) => {
                // 如果是组件，添加默认值 并且 不是修改
                if(formData.type == 1 && !updateComponent.value){
                    let sourceCode = `<template>

</template>

<script setup>

<\/script>`
                    let compileJs = `const __sfc__ = {}
function render(_ctx, _cache) {
  return null
}
__sfc__.render = render
__sfc__.__file = "mb-sfc-compiler.vue"
return __sfc__`
                    let compileCss = `/* No <style> tags present */`
                    $common.post('/system/component/saveCode', {
                        id: res.data,
                        sourceCode,
                        compileJs,
                        compileCss
                    })
                }
                nameModal.value.hide()
                if(updateComponent.value){
                    tabs.value.forEach(it => {
                        if(it.id == currentNodeId.value){
                            it.name = formData.name
                        }
                    })
                }
                treeRef.value.reload()
            })
        }
    })
}
function nodeClick(option){
    if(!option.isGroup){
        if(tabs.value.some(it => it.id == option.id)){
            currentNodeId.value = option.id
        }else{
            $common.get('/system/component/getSourceCode', { id: option.id }).then(res => {
                const { sourceCode, dynamicMode } = res.data
                tabs.value.push({
                    id: option.id,
                    name: option.name,
                    code: sourceCode || '',
                    dynamicMode: dynamicMode || '1',
                    isSave: 0
                })
                currentNodeId.value = option.id
            })
        }
    }
}

const dataForm = ref()
const rules = reactive({
    name: [{required: true, message: '请输入名称', trigger: 'blur'}, {
        trigger: 'blur',
        message: '名称只能包含大小写英文、数字和-_',
        validator: (rule, value) => {
            if(value){
                return /^[a-zA-Z0-9\-_]+$/.test(value)
            }
        }
    }],
    remark: [{
        trigger: 'blur',
        message: '备注不能包含()',
        validator: (rule, value) => {
            return !/\(|\)/g.test(value)
        }
    }],
})

const tabs = ref([])
function tabClose(id){
    const index = tabs.value.findIndex((it) => it.id == id);
    tabs.value.splice(index, 1);
    if(tabs.value.length > 0){
        currentNodeId.value = tabs.value[tabs.value.length - 1].id
    }
    monacoVolarRefs[id] && monacoVolarRefs[id].dispose()
}
function setComponentRef(el, id){
    monacoVolarRefs[id] = el
}
function saveCode(){
    try{
        let id = currentNodeId.value

        let componentName = tabs.value.filter(it => it.id == currentNodeId.value)[0].name
        if(componentName.indexOf('(') !== -1){
            componentName = analyzeName(componentName, true)
        }
        let sourceCode = monacoVolarRefs[id].getValue()
        const { compileJs, compileCss } = compileCode(sourceCode, componentName)
        let tab = tabs.value.filter(it => it.id == id)[0]
        if(tab.isSave == 1){
            tab.code = sourceCode
            tab.isSave = 0
            $common.post('/system/component/saveCode', {
                id,
                sourceCode,
                compileJs,
                compileCss
            }).then(() => {
                $message.success('编译并保存成功')
            })
        }
    }catch (e){
        errorInfo.value = e
        errorInfoModal.value.show()
    }
}
function onDidChangeModelContent(tab){
    if(monacoVolarRefs[tab.id].getValue() != tab.code){
        tab.isSave = 1
    }else{
        tab.isSave = 0
    }
}
function restoreToThisVersion(){
    monacoVolarRefs[currentNodeId.value].setValue(historyOldCode.value)
    showDrawer.value = false
}
function updateDynamicMode(mode){
    currentNode.value.dynamicMode = mode
    $common.post('/system/component/updateDynamicMode', {
        id: currentNodeId.value,
        dynamicMode: mode
    }).then(() => {
        $message.success('更新成功')
    })
}
</script>
