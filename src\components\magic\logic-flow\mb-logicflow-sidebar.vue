<template>
  <div class="mb-logicflow-sidebar">
    <!-- 搜索框 -->
    <div class="sidebar-search">
      <n-input
        v-model="searchKeyword"
        placeholder="搜索形状..."
        size="small"
        clearable
      >
        <template #prefix>
          <n-icon>
            <search-outline />
          </n-icon>
        </template>
      </n-input>
    </div>

    <!-- 基础形状节点 -->
    <div class="node-groups">
      <div class="node-group">
        <h3 class="group-title">基础形状</h3>
        <div class="node-grid">
          <div
            v-for="shape in filteredShapes"
            :key="shape.type"
            class="node-item"
            @mousedown="dragInNode(shape)"
          >
            <div class="node-icon">
              <div :class="['shape-preview', `shape-${shape.type}`]">
                <component :is="shape.component" />
              </div>
            </div>
            <div class="node-label">{{ shape.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, h } from 'vue'
import { NInput, NIcon } from 'naive-ui'
import { SearchOutline } from '@vicons/ionicons5'

export default {
  name: 'MbLogicflowSidebar',
  components: {
    NInput,
    NIcon,
    SearchOutline
  },
  emits: ['dragInNode'],
  setup(props, { emit }) {
    const searchKeyword = ref('')

    /**
     * 基础形状配置
     */
    const basicShapes = [
      {
        type: 'circle',
        name: '圆形',
        defaultWidth: 100,
        defaultHeight: 100,
        component: () => h('div', { 
          style: { 
            width: '24px', 
            height: '24px', 
            border: '2px solid #1890ff', 
            borderRadius: '50%',
            backgroundColor: 'transparent'
          } 
        })
      },
      {
        type: 'square',
        name: '正方形',
        defaultWidth: 100,
        defaultHeight: 100,
        component: () => h('div', { 
          style: { 
            width: '24px', 
            height: '24px', 
            border: '2px solid #1890ff',
            backgroundColor: 'transparent'
          } 
        })
      },
      {
        type: 'rectangle',
        name: '长方形',
        defaultWidth: 140,
        defaultHeight: 80,
        component: () => h('div', { 
          style: { 
            width: '32px', 
            height: '20px', 
            border: '2px solid #1890ff',
            backgroundColor: 'transparent'
          } 
        })
      },
      {
        type: 'ellipse',
        name: '椭圆',
        defaultWidth: 140,
        defaultHeight: 80,
        component: () => h('div', { 
          style: { 
            width: '32px', 
            height: '20px', 
            border: '2px solid #1890ff', 
            borderRadius: '50%',
            backgroundColor: 'transparent'
          } 
        })
      },
      {
        type: 'diamond',
        name: '菱形',
        defaultWidth: 120,
        defaultHeight: 120,
        component: () => h('div', { 
          style: { 
            width: '24px', 
            height: '24px', 
            border: '2px solid #1890ff',
            backgroundColor: 'transparent',
            transform: 'rotate(45deg)'
          } 
        })
      },
      {
        type: 'card',
        name: '横向长条形',
        defaultWidth: 140,  // 拖拽到画布的尺寸保持原来大小
        defaultHeight: 40,  // 拖拽到画布的尺寸保持原来大小
        component: () => {
          // 预览尺寸
          const previewWidth = 45
          const previewHeight = 14
          
          // 参数设置（缩放比例）
          const margin = 1 // 整体边距（缩放）
          const textMargin = 1 // 文字区域边距（缩放）
          const iconAreaSize = previewHeight - margin * 2
          const textAreaX = margin + iconAreaSize
          const textAreaWidth = previewWidth - (iconAreaSize + margin) - textMargin
          const textAreaHeight = previewHeight - textMargin * 2
          const cornerRadius = 3 // 圆角（缩放）
          
                     return h('svg', {
             style: { width: `${previewWidth}px`, height: `${previewHeight}px` }
           }, [
             // 整体背景层（紫色）
             h('rect', {
               x: 0,
               y: 0,
               width: previewWidth,
               height: previewHeight,
               rx: cornerRadius,
               ry: cornerRadius,
               fill: '#8B5CF6'
             }),
             // 右侧文字区域（白色，选择性圆角）
             h('path', {
               d: [
                 `M ${textAreaX} ${textMargin}`,
                 `L ${textAreaX + textAreaWidth - cornerRadius} ${textMargin}`,
                 `Q ${textAreaX + textAreaWidth} ${textMargin} ${textAreaX + textAreaWidth} ${textMargin + cornerRadius}`,
                 `L ${textAreaX + textAreaWidth} ${textMargin + textAreaHeight - cornerRadius}`,
                 `Q ${textAreaX + textAreaWidth} ${textMargin + textAreaHeight} ${textAreaX + textAreaWidth - cornerRadius} ${textMargin + textAreaHeight}`,
                 `L ${textAreaX} ${textMargin + textAreaHeight}`,
                 `Z`
               ].join(' '),
               fill: '#ffffff'
             })
           ])
        }
      }
    ]

    /**
     * 过滤形状
     */
    const filteredShapes = computed(() => {
      if (!searchKeyword.value) {
        return basicShapes
      }
      return basicShapes.filter(shape => 
        shape.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
      )
    })

    /**
     * 开始拖拽节点
     */
    const dragInNode = (shape) => {
      const nodeData = {
        type: 'route-node',
        shapeType: shape.type,
        properties: {
          functionName: `新建${shape.name}`,
          iconSvg: shape.type === 'card' ? '#mb-icon-home' : '',  // card类型默认使用home图标
          routePath: '',
          description: `${shape.name}节点描述`,
          width: shape.defaultWidth,
          height: shape.defaultHeight,
          shapeType: shape.type,
          backgroundColor: shape.type === 'card' ? '#8B5CF6' : '#ffffff',  // card类型紫色背景，其他白色
          iconColor: shape.type === 'card' ? '#ffffff' : '#000000',       // card类型白色图标，其他黑色
          textColor: '#000000'        // 默认黑色文字
        }
      }
      
      console.log('🎯 开始拖拽形状节点:', {
        type: shape.type,
        name: shape.name,
        size: `${shape.defaultWidth}x${shape.defaultHeight}`
      })
      
      emit('dragInNode', nodeData)
    }

    return {
      searchKeyword,
      filteredShapes,
      dragInNode
    }
  }
}
</script>

<style scoped>
.mb-logicflow-sidebar {
  width: 100%;
  height: 100%;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
  user-select: none;
}

.sidebar-search {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #ffffff;
}

.node-groups {
  padding: 16px;
}

.node-group {
  margin-bottom: 24px;
}

.group-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.node-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.node-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
}

.node-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  transform: translateY(-1px);
}

.node-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

.node-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
}

.shape-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-label {
  font-size: 11px;
  color: #595959;
  text-align: center;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style> 